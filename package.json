{"name": "@swift/source", "version": "0.0.0", "license": "UNLICENSED", "private": true, "scripts": {"prepare": "husky", "ts-check": "nx affected -t ts-check", "lint": "nx affected:lint --max-warnings=0", "format": "nx format:write", "test": "nx affected:test"}, "packageManager": "pnpm@10.10.0", "engines": {"npm": "please-use-pnpm", "node": ">=20.0.0", "yarn": "please-use-pnpm", "pnpm": ">=10.10.0"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@ai-sdk/react": "^1.2.12", "@apollo/client": "3.13.0-rc.0", "@apollo/experimental-nextjs-app-support": "^0.11.11", "@apollo/server": "^4.11.3", "@cdktf/provider-aws": "^19.57.0", "@cdktf/provider-random": "^11.1.1", "@hookform/resolvers": "^5.0.1", "@nestjs/apollo": "^13.0.3", "@nestjs/axios": "^4.0.0", "@nestjs/common": "^10.0.2", "@nestjs/core": "^10.0.2", "@nestjs/graphql": "^13.0.3", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^10.0.2", "@nx/eslint-plugin": "21.2.0", "@prisma/client": "^6.5.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.6", "@tanstack/react-query": "^5.75.2", "@tanstack/react-query-devtools": "^5.75.2", "@tanstack/react-table": "^8.21.3", "@types/marked": "^5.0.2", "@types/pg": "^8.11.11", "ai": "^4.3.16", "axios": "^1.6.0", "cdktf": "^0.20.11", "cdktf-cli": "^0.21.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "constructs": "^10.4.2", "csv-parser": "^3.0.0", "date-fns": "^4.1.0", "graphql": "^16.10.0", "jwks-rsa": "^3.2.0", "lucide-react": "^0.487.0", "marked": "^12.0.2", "next": "15.2.5", "next-zod-route": "^0.2.5", "nuqs": "^2.4.1", "oidc-client-ts": "^3.2.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pg": "^8.14.1", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.55.0", "recharts": "^2.15.2", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.0", "tailwind-merge": "^3.1.0", "tslib": "^2.8.1", "tw-animate-css": "^1.2.5", "uuid": "^11.1.0", "xlsx": "^0.18.5", "xml-js": "^1.6.11", "zod": "^3.24.2"}, "devDependencies": {"@eslint/compat": "^1.1.1", "@eslint/eslintrc": "^2.1.1", "@eslint/js": "^9.22.0", "@nestjs/schematics": "11.0.5", "@nestjs/testing": "^10.0.2", "@next/eslint-plugin-next": "^15.3.2", "@nx-tools/nx-prisma": "^6.5.0", "@nx/jest": "21.2.0", "@nx/js": "21.2.0", "@nx/nest": "21.2.0", "@nx/next": "21.2.0", "@nx/node": "21.2.0", "@nx/web": "21.2.0", "@nx/webpack": "21.2.0", "@swc-node/register": "~1.9.1", "@swc/cli": "0.6.0", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@swc/jest": "~0.2.36", "@tailwindcss/postcss": "^4", "@testing-library/dom": "10.4.0", "@testing-library/react": "16.1.0", "@types/express": "^5.0.1", "@types/jest": "^29.5.12", "@types/node": "~18.16.9", "@types/passport-jwt": "^4.0.1", "@types/react": "19.0.0", "@types/react-dom": "19.0.0", "@types/uuid": "^10.0.0", "ajv": "8.12.0", "ajv-formats": "2.1.1", "ajv-keywords": "5.1.0", "autoprefixer": "10.4.13", "babel-jest": "^29.7.0", "eslint": "^9.22.0", "eslint-config-next": "15.3.3", "eslint-config-prettier": "^10.1.1", "eslint-plugin-ban": "^2.0.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.1", "eslint-plugin-only-warn": "^1.1.0", "eslint-plugin-prefer-arrow": "^1.2.3", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-sort-keys-fix": "^1.1.2", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^16.0.0", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-environment-node": "^29.7.0", "jsonc-eslint-parser": "^2.1.0", "lint-staged": "^15.5.0", "nx": "21.2.0", "postcss": "8.4.38", "prettier": "3.5.3", "prisma": "^6.5.0", "tailwindcss": "3.4.3", "tailwindcss-v4": "npm:tailwindcss@^4", "terraform": "^1.23.0", "ts-jest": "^29.1.0", "ts-morph": "^25.0.1", "ts-node": "10.9.1", "typescript": "5.8.3", "typescript-eslint": "^8.26.1", "webpack-cli": "^5.1.4"}, "hooks": {"pre-commit": "pnpm test"}, "overrides": {"react-is": "19.0.0"}}