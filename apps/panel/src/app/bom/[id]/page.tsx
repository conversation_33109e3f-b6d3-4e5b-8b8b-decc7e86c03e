"use client";

import { useQuery } from "@apollo/client";
import { useParams } from "next/navigation";
import React, { useRef } from "react";

import { BomQueryResult, GET_BOM } from "@/queries/bom_items";

import {
  BomEmptyState,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  BomItemDialog,
  BomTable,
  DeleteBomItemDialog,
} from "./_components";
import { BomItemDialogHandle } from "./_components/bom-item-dialog";
import { DeleteBomItemDialogHandle } from "./_components/delete-bom-item-dialog";
import { PartSelectionDialog } from "./_components/part-selection-dialog/part-selection-dialog";
import { PartSelectionDialogRef } from "./_components/part-selection-dialog/part-selection-dialog.type";

export default function BomDetailsPage() {
  const params = useParams();
  const bomId = parseInt(params.id as string, 10);

  const bomItemDialogRef = useRef<BomItemDialogHandle | null>(null);
  const deleteBomItemDialogRef = useRef<DeleteBomItemDialogHandle | null>(null);
  const partSelectionDialogRef = useRef<PartSelectionDialogRef | null>(null);

  const { data, loading, refetch } = useQuery<BomQueryResult>(GET_BOM, {
    skip: !bomId,
    variables: { bomId },
  });

  const bomItems = [...(data?.bom.bom_items || [])].sort(
    (a, b) =>
      new Date(a.created_at).getTime() - new Date(b.created_at).getTime(),
  );
  const hasBomItems = bomItems.length > 0;
  const bomName = data?.bom.name || "Loading...";

  const handleOpenCreateDialog = () => {
    bomItemDialogRef.current?.openForCreate();
  };

  if (loading && !data) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-500"></div>
        <p className="ml-2 text-gray-600">Loading BOM details...</p>
      </div>
    );
  }

  return (
    <div>
      <BomHeader bomName={bomName} onAddItem={handleOpenCreateDialog} />

      {!hasBomItems ? (
        <BomEmptyState onAddItem={handleOpenCreateDialog} />
      ) : (
        <BomTable
          bomItems={bomItems}
          bomItemDialogRef={bomItemDialogRef}
          deleteBomItemDialogRef={deleteBomItemDialogRef}
          partSelectionDialogRef={partSelectionDialogRef}
          bomId={bomId}
        />
      )}

      <BomItemDialog
        ref={bomItemDialogRef}
        bomId={bomId}
        onCompleted={refetch}
      />

      <DeleteBomItemDialog ref={deleteBomItemDialogRef} onCompleted={refetch} />

      <PartSelectionDialog ref={partSelectionDialogRef} bomId={bomId} />
    </div>
  );
}
