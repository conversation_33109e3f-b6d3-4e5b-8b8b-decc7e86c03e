import React from "react";

import { cn } from "@/lib/utils";

import useAllocatedOfferCell from "./allocated-offer-cell.hook";
import { AllocatedOfferCellProps } from "./allocated-offer-cell.type";

const AllocatedOfferCell: React.FC<AllocatedOfferCellProps> = ({
  supply,
  matchedStatus,
  quantity = 1,
  onClick,
  isExpanded,
}) => {
  const { formattedPrice, sku, sellerName, stock } = useAllocatedOfferCell(
    supply,
    quantity,
  );

  if (matchedStatus === "Unmatched" || !supply) {
    return <span className="text-xs text-gray-500">No offer found.</span>;
  }

  return (
    <div
      className={cn(
        "flex flex-col text-xs  rounded-sm cursor-pointer  h-[76px] content-between [&>span]:flex-1 hover:bg-blue-50/75 w-[130px]",
        isExpanded && "bg-blue-50/75",
      )}
      onClick={onClick}
    >
      <div className="flex flex-col">
        <span className="font-medium">{sellerName}</span>
        <span className="text-[10px]">{sku}</span>
      </div>

      <span className="text-gray-600">
        Available Stock:{" "}
        {typeof stock === "number" ? stock.toLocaleString() : stock}
      </span>
      <span className="text-gray-600">Unit Price: {formattedPrice}</span>
    </div>
  );
};

export default AllocatedOfferCell;
