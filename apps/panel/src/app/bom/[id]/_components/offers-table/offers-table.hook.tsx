import { useQuery } from "@apollo/client";
import {
  ColumnDef,
  createColumnHelper,
  getCoreRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from "@tanstack/react-table";
import { Download } from "lucide-react";
import { useMemo, useState } from "react";

import { ActionCell } from "@/components/ui/action-cell";
import {
  GET_SUPPLIES_BY_PART_ID,
  SuppliesByPartIdResponse,
} from "@/queries/supplier";
import { BomItem } from "@/queries/types";

import { getNumericPriceForQuantity } from "../utils";
import { OffersTableProps, Supply } from "./offers-table.type";
import {
  findOverallMinimumPriceAndSupplier,
  standardPriceBreaks,
} from "./offers-table.util";
import { PriceDisplay } from "./price-display";
import { PriceList } from "./price-list";
import { SelectOfferCell } from "./select-offer-cell";

const columnHelper = createColumnHelper<Supply>();

export const useSuppliesData = (partId: number) => {
  const { data, loading, error } = useQuery<SuppliesByPartIdResponse>(
    GET_SUPPLIES_BY_PART_ID,
    {
      variables: { partId },
      fetchPolicy: "cache-and-network",
      errorPolicy: "all",
      skip: !partId || isNaN(partId),
      notifyOnNetworkStatusChange: true,
    },
  );

  const supplies = (data?.suppliesByPartId as Supply[]) || [];

  const firstSupply = supplies[0];

  const partDetails = firstSupply?.part
    ? {
        description: firstSupply.part.description,
        manufacturer: firstSupply.part.manufacturer.name,
        mpn: firstSupply.part.mpn,
      }
    : null;

  return {
    loading,
    partDetails,
    supplies,
    error,
  };
};

const useOffersColumns = (
  supplies: Supply[],
  showAllPrices: boolean,
  bomId: number,
  bomItem: BomItem,
): ColumnDef<Supply, any>[] =>
  useMemo(() => {
    const priceBreakColumns = standardPriceBreaks.map((qty) =>
      columnHelper.accessor(
        (row) => getNumericPriceForQuantity(row.prices, qty),
        {
          cell: ({ row }) => {
            if (!row.original.prices || row.original.prices.length === 0) {
              return <div className="text-gray-400">-</div>;
            }

            const currentPrice = getNumericPriceForQuantity(
              row.original.prices,
              qty,
            );

            if (currentPrice === Number.MAX_VALUE) {
              return <div className="text-gray-400">-</div>;
            }

            return (
              <div className="whitespace-nowrap text-xs">
                <PriceDisplay prices={row.original.prices} quantity={qty} />
              </div>
            );
          },
          header: () => <div>{qty}+</div>,
          id: `price_${qty}`,
          sortingFn: "basic",
        },
      ),
    );

    return [
      columnHelper.display({
        cell: ({ row }) => (
          <div className="flex items-center justify-center transition-opacity">
            <SelectOfferCell
              supplyId={Number(row.original.id)}
              bomId={bomId}
              bomItem={bomItem}
            />
          </div>
        ),
        header: () => null,
        id: "select",
      }),
      columnHelper.accessor((row) => row.seller.name, {
        cell: ({ row }) => (
          <div className="font-medium min-w-[140px]">
            <div className="text-blue-600 hover:text-blue-800">
              <a
                href={row.original.product_url}
                target="_blank"
                rel="noopener noreferrer"
                className="hover:underline"
              >
                {row.original.seller.name}
              </a>
            </div>
            <div className="text-xs text-gray-500 text-[10px]">
              {row.original.sku}
            </div>
          </div>
        ),
        enableSorting: true,
        header: "Distributor",
        id: "distributor",
      }),
      columnHelper.accessor("stock", {
        cell: ({ getValue }) => {
          const stock = getValue();
          return (
            <div
              className={`text-right ${stock > bomItem.quantity ? "text-green-700 font-medium" : "text-gray-500"}`}
            >
              {stock.toLocaleString()}
            </div>
          );
        },
        enableSorting: true,
        header: "Stock",
      }),
      columnHelper.accessor("min_order_quantity", {
        cell: ({ getValue }) => {
          const moq = getValue() || 1;
          return <div>{moq.toLocaleString()}</div>;
        },
        enableSorting: true,
        header: "MOQ",
      }),
      columnHelper.accessor("packaging", {
        cell: ({ getValue }) => {
          const packaging = getValue();
          return <div>{packaging || "-"}</div>;
        },
        enableSorting: true,
        header: "Packaging",
      }),
      columnHelper.accessor("lead_time", {
        cell: ({ getValue }) => {
          const value = getValue();
          return <div>{value ? `${value}` : "-"}</div>;
        },
        enableSorting: true,
        header: "Lead Time (weeks)",
      }),
      ...priceBreakColumns,
      columnHelper.display({
        cell: ({ row }) => (
          <div className="min-w-[140px]">
            <PriceList
              prices={row.original.prices}
              defaultExpanded={showAllPrices}
            />
          </div>
        ),
        header: "All Price Breaks",
        id: "allPrices",
      }),
      columnHelper.display({
        cell: ({ row }) => {
          const actions = [];

          if (row.original.datasheet_url) {
            actions.push({
              className: "text-blue-600 hover:text-blue-800",
              icon: <Download className="h-3 w-3" />,
              label: "Download Datasheet",
              onClick: () => {
                window.open(row.original.datasheet_url, "_blank");
              },
            });
          }

          return actions.length > 0 ? <ActionCell actions={actions} /> : null;
        },
        header: "",
        id: "actions",
      }),
    ];
  }, [showAllPrices, bomId, bomItem]);

export const useOffersTable = ({ bomItem, bomId }: OffersTableProps) => {
  const [showAllPrices, setShowAllPrices] = useState(false);
  const [sorting, setSorting] = useState<SortingState>([]);

  const partId = bomItem?.part?.id;
  const validPartId = partId && !isNaN(Number(partId)) ? Number(partId) : 0;

  const {
    supplies,
    loading,
    partDetails: suppliesPartDetails,
    error,
  } = useSuppliesData(validPartId);

  const partDetails =
    suppliesPartDetails ||
    (bomItem?.part
      ? {
          description: bomItem.part.description,
          manufacturer: bomItem.part.manufacturer?.name || "Unknown",
          mpn: bomItem.part.mpn,
        }
      : null);

  const columns = useOffersColumns(supplies, showAllPrices, bomId, bomItem);

  const overallMinPriceData = useMemo(
    () => findOverallMinimumPriceAndSupplier(supplies),
    [supplies],
  );

  const table = useReactTable({
    columns,
    data: supplies,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onSortingChange: setSorting,
    state: {
      sorting,
    },
  });

  const rows = useMemo(() => {
    const sortedRows = [...table.getRowModel().rows];

    sortedRows.sort((a, b) => {
      const aHasStock = a.original.stock > 0;
      const bHasStock = b.original.stock > 0;

      if (aHasStock !== bHasStock) {
        return aHasStock ? -1 : 1;
      }

      return 0;
    });

    return sortedRows;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [table.getRowModel().rows]);

  return {
    loading,
    overallMinPriceData,
    partDetails,
    rows,
    setShowAllPrices,
    showAllPrices,
    supplies,
    table,
    error,
  };
};
