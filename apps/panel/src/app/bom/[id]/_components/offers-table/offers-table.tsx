import { flexRender } from "@tanstack/react-table";
import {
  ArrowDownIcon,
  ArrowUpDownIcon,
  ArrowUpIcon,
  BadgePercent,
  Loader2,
} from "lucide-react";
import React from "react";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ToggleSwitch } from "@/components/ui/toggle-switch";
import { cn } from "@/lib/utils";

import { useOffersTable } from "./offers-table.hook";
import { OffersTableProps } from "./offers-table.type";

const OffersTable = ({ bomItem, bomId }: OffersTableProps) => {
  const {
    table,
    rows,
    loading,
    partDetails,
    showAllPrices,
    setShowAllPrices,
    overallMinPriceData,
    error,
  } = useOffersTable({ bomId, bomItem });

  if (loading) {
    return (
      <div className="bg-white shadow-md rounded-lg p-6 flex items-center justify-center">
        <div className="flex items-center text-gray-500">
          <Loader2 className="mr-2 h-5 w-5 animate-spin" />
          Loading offers...
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white shadow-md rounded-lg p-6">
        <p className="text-red-500">Error loading offers: {error.message}</p>
      </div>
    );
  }

  if (!loading && !partDetails && !error) {
    return (
      <div className="bg-white shadow-md rounded-lg p-6">
        <p className="text-gray-500">No offers found for this part.</p>
      </div>
    );
  }

  if (!partDetails) {
    return (
      <div className="bg-white shadow-md rounded-lg p-6 flex items-center justify-center">
        <div className="flex items-center text-gray-500">
          <Loader2 className="mr-2 h-5 w-5 animate-spin" />
          Loading part details...
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white shadow-md rounded-lg w-full flex flex-col">
      <div className="p-4 bg-gray-100 border-b flex items-center justify-between">
        <div className="flex items-center gap-2">
          <ToggleSwitch
            label="Show All Prices"
            checked={showAllPrices}
            onChange={() => setShowAllPrices(!showAllPrices)}
          />
        </div>
      </div>

      <div className="p-3 border-b">
        <div className="flex flex-col">
          <h2 className="text-lg font-semibold text-gray-800">
            {partDetails.mpn}
          </h2>
          <p className="text-sm text-gray-600">{partDetails.description}</p>
          <p className="text-sm text-gray-600">
            Manufacturer: {partDetails.manufacturer}
          </p>
          {overallMinPriceData.minPrice > 0 && (
            <div className="flex items-center gap-1 mt-1">
              <BadgePercent className="h-4 w-4 text-gray-600" />
              <span className="font-medium">Min Price:</span>
              <span className="font-semibold">
                ${overallMinPriceData.minPrice.toFixed(4)}
              </span>
              <span className="text-gray-500">
                ({overallMinPriceData.supplier || "N/A"})
              </span>
            </div>
          )}
        </div>
      </div>

      <div className="overflow-hidden">
        <div style={{ maxHeight: "400px", overflow: "auto" }}>
          <Table>
            <TableHeader className="sticky top-0 z-10 bg-white">
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <TableHead
                      key={header.id}
                      onClick={
                        header.column.getCanSort()
                          ? header.column.getToggleSortingHandler()
                          : undefined
                      }
                      className={`${
                        header.column.getCanSort()
                          ? "cursor-pointer hover:bg-gray-100"
                          : ""
                      } text-xs`}
                    >
                      <div className="flex items-center">
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext(),
                            )}
                        {header.column.getCanSort() && (
                          <span className="ml-1 flex items-center">
                            {header.column.getIsSorted() === "asc" ? (
                              <ArrowUpIcon className="h-4 w-4" />
                            ) : header.column.getIsSorted() === "desc" ? (
                              <ArrowDownIcon className="h-4 w-4" />
                            ) : (
                              <ArrowUpDownIcon className="h-4 w-4 text-gray-300" />
                            )}
                          </span>
                        )}
                      </div>
                    </TableHead>
                  ))}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {rows?.length ? (
                rows.map((row) => (
                  <TableRow
                    key={row.id}
                    className={cn(
                      `group transition-colors hover:bg-muted/50`,
                      row.original.stock <= 0 &&
                        "bg-gray-100 hover:bg-gray-100",
                      row.original.id === bomItem?.supply?.id && "bg-green-100",
                    )}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id} className="text-xs">
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext(),
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={table.getAllColumns().length}
                    className="h-24 text-center"
                  >
                    No offers available.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
};

export default OffersTable;
