import { useMutation } from "@apollo/client";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "lucide-react";
import React, { FC } from "react";

import { Button } from "@/components/ui/button";
import { GET_BOM, UPDATE_BOM_ITEM } from "@/queries/bom_items";
import { GET_SUPPLIES_BY_PART_ID } from "@/queries/supplier";
import { BomItem, UpdateBomItemMutationResult } from "@/queries/types";

interface SelectOfferCellProps {
  bomItem: BomItem;
  supplyId: number;
  bomId: number;
}

export const SelectOfferCell: FC<SelectOfferCellProps> = ({
  supplyId,
  bomId,
  bomItem,
}) => {
  const [updateBomItem, { loading: isUpdating }] = useMutation<
    UpdateBomItemMutationResult,
    {
      updateBomItemInput: {
        id: number;
        supply_id: number;
      };
    }
  >(UPDATE_BOM_ITEM, {
    onCompleted: () => {},
  });

  const handleUpdateSelectedSupply = () => {
    const partId = bomItem.part?.id;

    if (!partId) {
      console.warn("Cannot update supply: part ID is missing");
      return;
    }

    updateBomItem({
      refetchQueries: [
        { query: GET_BOM, variables: { bomId } },
        {
          query: GET_SUPPLIES_BY_PART_ID,
          variables: { partId },
        },
      ],
      awaitRefetchQueries: true,
      variables: {
        updateBomItemInput: { id: Number(bomItem.id), supply_id: supplyId },
      },
      onError: (error) => {
        console.error("Error updating BOM item:", error);
      },
    });
  };
  if (supplyId === Number(bomItem?.supply?.id)) {
    return (
      <Button variant="outline" size="sm" disabled className="gap-1">
        <CircleCheck className="h-4 w-4" /> SELECTED
      </Button>
    );
  }

  return (
    <Button
      variant="outline"
      size="sm"
      disabled={isUpdating}
      onClick={handleUpdateSelectedSupply}
      className="gap-1"
    >
      <CheckCircle2 className="h-4 w-4" /> SELECT
    </Button>
  );
};
