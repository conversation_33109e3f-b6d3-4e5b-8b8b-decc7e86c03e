import {
  ColumnDef,
  getCoreRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from "@tanstack/react-table";
import { PencilIcon, Trash2Icon } from "lucide-react";
import React, { useCallback, useMemo, useState } from "react";

import { QuoteButtonCell } from "@/app/_components/product-results/quote-button-cell";
import { Button } from "@/components/ui/button";
import { BomItem } from "@/queries/types";

import { AllocatedOfferCell } from "../allocated-offer-cell";
import { calculateTotalPrice } from "../utils";
import { BomTableProps } from "./bom-table.type";
import { MarketInfoCell } from "./market-info-cell";
import { MatchStatusCell } from "./match-status-cell";

export const useBomTable = ({
  bomItemDialogRef,
  bomItems,
  deleteBomItemDialogRef,
  partSelectionDialogRef,
}: BomTableProps) => {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [expandedRow, setExpandedRow] = useState<number | null>(null);

  const toggleRowExpansion = useCallback((id: number) => {
    setExpandedRow((prev) => (prev === id ? null : id));
  }, []);

  const handleMatchStatusClick = useCallback(
    (bomItem: BomItem) => {
      partSelectionDialogRef.current?.open(bomItem);
    },
    [partSelectionDialogRef],
  );

  const columns = useMemo<ColumnDef<BomItem>[]>(
    () => [
      {
        accessorKey: "idx",
        cell: ({ row }) => row.index + 1,
        enableSorting: false,
        header: "#",
      },
      {
        accessorKey: "mpn",
        cell: ({ row }) => (
          <div className="flex items-start gap-1 flex-col ">
            <span>{row.original.mpn || "N/A"}</span>
            <span>{row.original.manufacturer_name || "N/A"}</span>
          </div>
        ),
        enableSorting: false,
        header: "Original Part Number",
      },
      {
        cell: ({ row }) => (
          <div className="flex items-start gap-1 flex-col ">
            {row.original.matched_status === "NotMatched" ? (
              <MatchStatusCell
                matchedStatus={row.original.matched_status}
                onClick={() => handleMatchStatusClick(row.original)}
              />
            ) : (
              <>
                <span>{row.original.part?.mpn || "N/A"}</span>
                <span>{row.original.part?.manufacturer.name || "N/A"}</span>
                <MatchStatusCell
                  matchedStatus={row.original.matched_status}
                  onClick={() => handleMatchStatusClick(row.original)}
                />
              </>
            )}
          </div>
        ),
        enableSorting: false,
        header: "Matched Part Number",
      },
      {
        accessorKey: "quantity",
        cell: ({ row }) => row.original.quantity,
        enableSorting: true,
        header: "Quantity",
      },
      {
        accessorKey: "part.description",
        cell: ({ row }) => (
          <div className="flex items-start gap-1 flex-col max-w-[220px] ">
            {row.original.part?.description || "N/A"}
          </div>
        ),
        enableSorting: false,
        header: "Description",
      },
      {
        accessorKey: "market_info",
        cell: ({ row }) => <MarketInfoCell bomItem={row.original} />,
        enableSorting: false,
        header: "Market Info",
      },
      {
        accessorKey: "allocated_offer",
        cell: ({ row }) => (
          <AllocatedOfferCell
            supply={row.original.supply}
            matchedStatus={row.original.matched_status}
            quantity={row.original.quantity}
            onClick={() => {
              toggleRowExpansion(Number(row.original.id));
            }}
            isExpanded={expandedRow === Number(row.original.id)}
          />
        ),
        enableSorting: false,
        header: "Allocated Offer",
      },
      {
        cell: ({ row }) => (
          <div className="flex items-start gap-1 flex-col w-24 ">
            <span>
              Lifecycle: {row.original.supply?.part?.lifecycle_status || "N/A"}
            </span>
            <span>
              Compliance: {row.original.supply?.part?.rohs_status || "N/A"}
            </span>
          </div>
        ),
        enableSorting: false,
        header: "Part Info",
      },
      {
        accessorKey: "packaging",
        cell: ({ row }) => (
          <div className="flex items-start gap-1 flex-col ">
            <span>{row.original.supply?.packaging || "N/A"}</span>
            <span>MOQ: {row.original.supply?.min_order_quantity || "N/A"}</span>
          </div>
        ),
        enableSorting: false,
        header: "Packaging",
      },
      {
        accessorFn: (row) => {
          if (
            !row.supply ||
            !row.supply.prices ||
            row.supply.prices.length === 0
          )
            return -1;

          const totalPrice = calculateTotalPrice(
            row.supply.prices,
            row.quantity,
          );

          if (
            totalPrice === 0 ||
            !isFinite(totalPrice) ||
            totalPrice >= Number.MAX_SAFE_INTEGER
          ) {
            return -1;
          }

          return totalPrice;
        },
        cell: ({ row }) => {
          if (
            !row.original.supply ||
            !row.original.supply.prices ||
            row.original.supply.prices.length === 0
          )
            return "N/A";

          const totalPrice = calculateTotalPrice(
            row.original.supply.prices,
            row.original.quantity,
          );

          if (
            totalPrice === 0 ||
            !isFinite(totalPrice) ||
            totalPrice >= Number.MAX_SAFE_INTEGER
          ) {
            return "N/A";
          }

          return `$${totalPrice.toFixed(2)}`;
        },
        enableSorting: true,
        header: "Total price",
        id: "total_price",
      },
      {
        cell: ({ row }) => {
          const shouldShowQuote =
            row.original.supply?.seller.key === "DIGIKEY" && row.original.part;
          return (
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
                  e.stopPropagation();
                  bomItemDialogRef.current?.openForEdit(row.original);
                }}
              >
                <PencilIcon className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
                  e.stopPropagation();
                  deleteBomItemDialogRef.current?.open(row.original);
                }}
              >
                <Trash2Icon className="h-4 w-4" />
              </Button>
              <QuoteButtonCell
                product={{
                  description: row.original.part?.description || "",
                  leadTime: row.original.supply?.lead_time || "",
                  mpn: row.original.part?.mpn || "",
                  supplies: [],
                }}
                supply={{
                  minOrderQuantity:
                    row.original.supply?.min_order_quantity || 0,
                  sku: row.original.supply?.sku || "",
                  stock: row.original.supply?.stock || 0,
                  supplier: row.original.supply?.seller?.name || "",
                }}
                disabled={!shouldShowQuote}
                defaultQuantity={row.original.quantity}
              />
            </div>
          );
        },
        enableSorting: false,
        header: "Actions",
        id: "actions",
      },
    ],
    [
      bomItemDialogRef,
      deleteBomItemDialogRef,
      expandedRow,
      toggleRowExpansion,
      handleMatchStatusClick,
    ],
  );

  const table = useReactTable({
    columns,
    data: bomItems.sort((a, b) => Number(a.created_at) - Number(b.created_at)),
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onSortingChange: setSorting,
    state: { sorting },
  });
  return {
    columns,
    expandedRow,
    table,
  };
};
