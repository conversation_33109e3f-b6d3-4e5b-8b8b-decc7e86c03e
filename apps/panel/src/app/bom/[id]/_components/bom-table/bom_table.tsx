import { flexRender } from "@tanstack/react-table";
import { ArrowDownIcon, ArrowUpDownIcon, ArrowUpIcon } from "lucide-react";
import React from "react";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

import { OffersTable } from "../offers-table";
import { useBomTable } from "./bom-table.hook";
import { BomTableProps } from "./bom-table.type";

const BomTable = ({
  bomItemDialogRef,
  bomItems,
  deleteBomItemDialogRef,
  partSelectionDialogRef,
  bomId,
}: BomTableProps) => {
  const { table, columns, expandedRow } = useBomTable({
    bomId,
    bomItemDialogRef,
    bomItems,
    deleteBomItemDialogRef,
    partSelectionDialogRef,
  });

  return (
    <div className="bg-white shadow-md rounded-lg">
      <div className="overflow-hidden rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead
                    key={header.id}
                    onClick={
                      header.column.getCanSort()
                        ? header.column.getToggleSortingHandler()
                        : undefined
                    }
                    className={
                      header.column.getCanSort()
                        ? "cursor-pointer hover:bg-gray-100"
                        : "text-xs"
                    }
                  >
                    <div className="flex items-center text-xs">
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                      {header.column.getCanSort() && (
                        <>
                          {header.column.getIsSorted() === "asc" ? (
                            <ArrowUpIcon className="ml-1 h-4 w-4" />
                          ) : header.column.getIsSorted() === "desc" ? (
                            <ArrowDownIcon className="ml-1 h-4 w-4" />
                          ) : (
                            <div className="ml-1 h-4 w-4">
                              <ArrowUpDownIcon className="h-4 w-4 text-gray-300" />
                            </div>
                          )}
                        </>
                      )}
                    </div>
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row, index) => (
                <React.Fragment key={row.index}>
                  <TableRow
                    data-state={row.getIsSelected() && "selected"}
                    className={index % 2 === 1 ? "bg-gray-50" : ""}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell key={cell.id} className="text-xs">
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext(),
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                  {expandedRow === Number(row.original.id) && (
                    <TableRow
                      className={index % 2 === 1 ? "bg-gray-100" : "bg-gray-50"}
                    >
                      <TableCell
                        colSpan={columns.length}
                        className="p-0 max-w-0 overflow-hidden"
                      >
                        <div className="w-full max-w-full overflow-x-auto">
                          {row.original.matched_part_id ? (
                            <OffersTable bomItem={row.original} bomId={bomId} />
                          ) : (
                            <div className="p-4 text-gray-500">
                              No matching part found. Match a part to see
                              offers.
                            </div>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </React.Fragment>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default BomTable;
