"use client";

import { Filter, Rotate<PERSON>cw } from "lucide-react";
import { usePathname, useRouter } from "next/navigation";

import { SelectField } from "@/components/primitives/form-select";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

import { ChartContainer } from "./_components/chart";
import { PartDetails } from "./_components/part-details";
import { PartSelector } from "./_components/part-selector";
import { breakMethodOptions, priceBreakOptions } from "./page.const";
import { useProductPage } from "./page.hook";

export default function Page() {
  const pathname = usePathname();
  const router = useRouter();
  const {
    selectedPart,
    priceBreakValues,
    setSelectedPart,
    setPriceBreakValues,
    chartsEnabled,
    handleReset,
    handleFilterClick,
  } = useProductPage();

  return (
    <div className="max-w-7xl mx-auto container py-10">
      <h1 className="text-3xl font-bold mb-6">Market Intelligence Dashboard</h1>

      <Tabs
        value={pathname === "/trend/product" ? "part" : "commodity"}
        onValueChange={(value) => {
          if (value === "commodity") {
            router.push("/trend");
          } else if (value === "part") {
            router.push("/trend/product");
          }
        }}
        className="w-full"
      >
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="commodity">Commodity</TabsTrigger>
          <TabsTrigger value="part">Part</TabsTrigger>
        </TabsList>

        <TabsContent value="commodity" className="mt-6">
          <div className="grid grid-cols-1 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Commodity Analysis</CardTitle>
                <CardDescription>
                  Analyze commodity trends and market data
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Commodity analysis is available on the main trend page.
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="part" className="mt-6">
          <div className="grid grid-cols-1 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Part Selection</CardTitle>
                <CardDescription>
                  Search and select a part from the inventory
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-8">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <PartSelector
                    value={selectedPart || ""}
                    defaultValue={
                      selectedPart ? parseInt(selectedPart, 10) : undefined
                    }
                    onValueChange={(value) => setSelectedPart(value || null)}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <SelectField
                      label="Price Break"
                      value={priceBreakValues.priceBreak || ""}
                      onValueChange={(value) =>
                        setPriceBreakValues((prev) => ({
                          ...prev,
                          priceBreak: value,
                        }))
                      }
                      options={priceBreakOptions}
                    />
                  </div>
                  <div>
                    <SelectField
                      label="Break Method"
                      value={priceBreakValues.breakMethod || ""}
                      onValueChange={(value) =>
                        setPriceBreakValues((prev) => ({
                          ...prev,
                          breakMethod: value as "min" | "avg",
                        }))
                      }
                      options={breakMethodOptions}
                    />
                  </div>
                </div>

                <div className="flex items-end justify-end gap-4">
                  <Button
                    variant="outline"
                    onClick={handleReset}
                    className="w-full md:w-auto"
                  >
                    <RotateCcw className="h-4 w-4 mr-2" />
                    Reset
                  </Button>

                  <Button
                    onClick={handleFilterClick}
                    className="w-full md:w-auto"
                    disabled={!selectedPart}
                  >
                    <Filter className="h-4 w-4 mr-2" />
                    Analyze
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card className="mt-6">
              <CardHeader>
                <CardTitle>Part Details</CardTitle>
              </CardHeader>
              <PartDetails partId={selectedPart} enabled={chartsEnabled} />
            </Card>

            <ChartContainer
              partId={selectedPart || undefined}
              enabled={Boolean(chartsEnabled)}
              priceBreak={priceBreakValues.priceBreak}
              breakMethod={priceBreakValues.breakMethod}
            />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
