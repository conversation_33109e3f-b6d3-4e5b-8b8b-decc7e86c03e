"use client";

import { Filter, RotateCcw } from "lucide-react";
import { usePathname, useRouter } from "next/navigation";
import { createParser, useQueryState } from "nuqs";
import { useCallback, useMemo, useRef, useState } from "react";
import { v4 as uuidv4 } from "uuid";

import { SelectField } from "@/components/primitives/form-select";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

import { ChartContainer, FilterSet } from "./_components/chart";
import { CommoditySelector } from "./_components/commodity-selector";
import { CommoditySelectorHandle } from "./_components/commodity-selector/commodity-selector.type";
import { FilterSetValue } from "./_components/filter-set";
import { FilterSetsManager } from "./_components/filter-sets-manager";
import { ProductLineSelector } from "./_components/product-line-selector";
import { breakMethodOptions, priceBreakOptions } from "./page.const";

export default function Dashboard() {
  const pathname = usePathname();
  const router = useRouter();
  const commoditySelectorRef = useRef<CommoditySelectorHandle>(null);

  //Create static UUIDs to prevent regeneration on every render
  const defaultFilterSetId = useMemo(() => uuidv4(), []);

  const selectedValuesParser = createParser({
    parse: (value) => {
      try {
        return JSON.parse(value) as {
          breakMethod: string;
          commodityId: string | undefined;
          priceBreak: string;
          productLineId: string | undefined;
          subCommodityId: string | undefined;
          subSubCommodityId: string | undefined;
        };
      } catch {
        return {
          breakMethod: "avg",
          commodityId: undefined,
          priceBreak: "1000",
          productLineId: undefined,
          subCommodityId: undefined,
          subSubCommodityId: undefined,
        };
      }
    },
    serialize: (value) => JSON.stringify(value),
  }).withDefault({
    breakMethod: "avg",
    commodityId: undefined,
    priceBreak: "1000",
    productLineId: undefined,
    subCommodityId: undefined,
    subSubCommodityId: undefined,
  });

  const filterSetsParser = createParser({
    parse: (value) => {
      try {
        return JSON.parse(value) as FilterSetValue[];
      } catch {
        return [
          {
            gradeId: undefined,
            id: defaultFilterSetId,
            manufacturerId: undefined,
            packagingId: undefined,
          },
        ];
      }
    },
    serialize: (value) => JSON.stringify(value),
  }).withDefault([
    {
      gradeId: undefined,
      id: defaultFilterSetId,
      manufacturerId: undefined,
      packagingId: undefined,
    },
  ]);

  const [selectedValues, setSelectedValues] = useQueryState(
    "selectedValues",
    selectedValuesParser,
  );
  const [filterSets, setFilterSets] = useQueryState(
    "filterSets",
    filterSetsParser,
  );
  const [chartsEnabled, setChartsEnabled] = useState(false);
  const [activeChartFilters, setActiveChartFilters] = useState<{
    filterSets: FilterSet[];
    baseFilters: {
      commodityId?: string;
      subCommodityId?: string;
      subSubCommodityId?: string;
      productLineId?: string;
      priceBreak?: string;
      breakMethod?: string;
    };
  }>({
    baseFilters: {
      breakMethod: "avg",
      commodityId: undefined,
      priceBreak: undefined,
      productLineId: undefined,
      subCommodityId: undefined,
      subSubCommodityId: undefined,
    },
    filterSets: [],
  });

  const handleCommodityChange = useCallback(
    (id: string | undefined) => {
      setSelectedValues((prev) => ({
        ...prev,
        commodityId: id,
        subCommodityId: undefined,
        subSubCommodityId: undefined,
      }));
    },
    [setSelectedValues],
  );

  const handleSubCommodityChange = useCallback(
    (id: string | undefined) => {
      setSelectedValues((prev) => ({
        ...prev,
        subCommodityId: id,
        subSubCommodityId: undefined,
      }));
    },
    [setSelectedValues],
  );

  const handleSubSubCommodityChange = useCallback(
    (id: string | undefined) => {
      setSelectedValues((prev) => ({
        ...prev,
        subSubCommodityId: id,
      }));
    },
    [setSelectedValues],
  );

  const handleFilterClick = () => {
    //Convert filter sets to the format expected by ChartContainer
    const chartFilterSets: FilterSet[] = filterSets.map((set) => ({
      gradeId: set.gradeId,
      id: set.id,
      manufacturerId: set.manufacturerId,
      packagingId: set.packagingId,
    }));

    const baseFilters = {
      breakMethod: selectedValues.breakMethod,
      commodityId: selectedValues.commodityId,
      priceBreak: selectedValues.priceBreak,
      productLineId: selectedValues.productLineId,
      subCommodityId: selectedValues.subCommodityId,
      subSubCommodityId: selectedValues.subSubCommodityId,
    };

    setActiveChartFilters({
      baseFilters,
      filterSets: chartFilterSets,
    });

    setChartsEnabled(true);
  };

  const handleResetClick = () => {
    if (commoditySelectorRef.current) {
      commoditySelectorRef.current.reset();
    }

    setSelectedValues({
      breakMethod: "avg",
      commodityId: undefined,
      priceBreak: "1000",
      productLineId: undefined,
      subCommodityId: undefined,
      subSubCommodityId: undefined,
    });

    setFilterSets([
      {
        gradeId: undefined,
        id: defaultFilterSetId,
        manufacturerId: undefined,
        packagingId: undefined,
      },
    ]);

    setChartsEnabled(false);

    setActiveChartFilters({
      baseFilters: {
        commodityId: undefined,
        priceBreak: undefined,
        productLineId: undefined,
        subCommodityId: undefined,
        subSubCommodityId: undefined,
      },
      filterSets: [],
    });
  };

  return (
    <div className="max-w-7xl mx-auto container py-10">
      <h1 className="text-3xl font-bold mb-6">Market Intelligence Dashboard</h1>

      <Tabs
        value={pathname === "/trend/product" ? "part" : "commodity"}
        onValueChange={(value) => {
          if (value === "commodity") {
            router.push("/trend");
          } else if (value === "part") {
            router.push("/trend/product");
          }
        }}
        className="w-full"
      >
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="commodity">Commodity</TabsTrigger>
          <TabsTrigger value="part">Part</TabsTrigger>
        </TabsList>

        <TabsContent value="commodity" className="mt-6">
          <div className="grid grid-cols-1 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Commodity Selection</CardTitle>
                <CardDescription>
                  Select commodities to filter market data
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-8">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                  <CommoditySelector
                    ref={commoditySelectorRef}
                    defaultCommodityId={selectedValues.commodityId}
                    defaultSubCommodityId={selectedValues.subCommodityId}
                    defaultSubSubCommodityId={selectedValues.subSubCommodityId}
                    onCommodityChange={handleCommodityChange}
                    onSubCommodityChange={handleSubCommodityChange}
                    onSubSubCommodityChange={handleSubSubCommodityChange}
                  />

                  <ProductLineSelector
                    value={selectedValues.productLineId}
                    onValueChange={(value) =>
                      setSelectedValues((prev) => ({
                        ...prev,
                        productLineId: value,
                      }))
                    }
                    commodityId={selectedValues.commodityId}
                    subCommodityId={selectedValues.subCommodityId}
                    subSubCommodityId={selectedValues.subSubCommodityId}
                  />
                </div>

                <FilterSetsManager
                  values={filterSets}
                  onValuesChange={setFilterSets}
                  commodityId={selectedValues.commodityId}
                  subCommodityId={selectedValues.subCommodityId}
                  subSubCommodityId={selectedValues.subSubCommodityId}
                  plId={selectedValues.productLineId}
                />
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <SelectField
                      label="Price Break"
                      value={selectedValues.priceBreak || ""}
                      onValueChange={(value) =>
                        setSelectedValues((prev) => ({
                          ...prev,
                          priceBreak: value,
                        }))
                      }
                      options={priceBreakOptions}
                    />
                  </div>
                  <div>
                    <SelectField
                      label="Break Method"
                      value={selectedValues.breakMethod || ""}
                      onValueChange={(value) =>
                        setSelectedValues((prev) => ({
                          ...prev,
                          breakMethod: value,
                        }))
                      }
                      options={breakMethodOptions}
                    />
                  </div>
                </div>
                <div className="flex items-end justify-end gap-4">
                  <Button
                    variant="outline"
                    onClick={handleResetClick}
                    className="w-full md:w-auto"
                  >
                    <RotateCcw className="h-4 w-4 mr-2" />
                    Reset
                  </Button>
                  <Button
                    onClick={handleFilterClick}
                    className="w-full md:w-auto"
                    disabled={
                      !selectedValues.commodityId || filterSets.length === 0
                    }
                  >
                    <Filter className="h-4 w-4 mr-2" />
                    {filterSets.length === 1
                      ? "Analyze"
                      : `Analyze ${filterSets.length} Filter Sets`}
                  </Button>
                </div>
              </CardContent>
            </Card>

            <ChartContainer
              filterSets={activeChartFilters.filterSets}
              baseFilters={activeChartFilters.baseFilters}
              enabled={chartsEnabled}
            />
          </div>
        </TabsContent>

        <TabsContent value="part" className="mt-6">
          <div className="grid grid-cols-1 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Part Analysis</CardTitle>
                <CardDescription>
                  Analyze individual part trends and market data
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Part analysis coming soon...
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
