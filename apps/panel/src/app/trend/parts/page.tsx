"use client";

import { usePathname, useRouter } from "next/navigation";
import { Suspense } from "react";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

import { PartsTable } from "./_components/parts-table";

export default function PartsPage() {
  const pathname = usePathname();
  const router = useRouter();

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-6">Market Intelligence Dashboard</h1>

      <Tabs
        value={pathname === "/trend/parts" ? "part" : "commodity"}
        onValueChange={(value) => {
          if (value === "commodity") {
            router.push("/trend");
          } else if (value === "part") {
            router.push("/trend/parts");
          }
        }}
        className="w-full"
      >
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="commodity">Commodity</TabsTrigger>
          <TabsTrigger value="part">Part</TabsTrigger>
        </TabsList>

        <TabsContent value="commodity" className="mt-6">
          <div className="grid grid-cols-1 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Commodity Analysis</CardTitle>
                <CardDescription>
                  Analyze commodity trends and market data
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Commodity analysis is available on the main trend page.
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="part" className="mt-6">
          <div className="bg-card rounded-lg shadow p-6">
            <Suspense fallback={<div>Loading...</div>}>
              <PartsTable />
            </Suspense>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
