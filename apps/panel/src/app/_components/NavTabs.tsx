"use client";

import { HandCoi<PERSON>, Search } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";

import { AuthNav } from "@/components/layout/auth-nav";

const tabs = [
  { href: "/", name: "Product Search" },
  { href: "/bom", name: "BOM Management" },
  { href: "/quotes", name: "Quote Requests" },
  { href: "/trend", name: "Market Intelligence" },
  { href: "#", name: "Supplier Management" },
  { href: "#", name: "Alarm & Notification" },
  { href: "#", name: "Report" },
];

export default function NavTabs() {
  const pathname = usePathname();
  return (
    <>
      <div className="bg-[#14233A]  py-2 px-4">
        <div className="max-w-[1440px] mx-auto flex items-center justify-between">
          {/* Left section with menu and title */}
          <div className="flex items-center space-x-4">
            {/* RFQ Management title */}
            <div className="flex items-center space-x-2">
              <HandCoins className="h-8 w-8 text-white bg-[#3A80E8] p-1 rounded-lg" />
              <span className="font-medium text-white">RFQ Management</span>
            </div>
          </div>

          {/* Search bar */}
          <div className="flex-1 max-w-xl mx-4">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search Product"
                className="block w-full pl-10 pr-3 py-1.5 rounded-md bg-[#1f324e] border border-[#2a3e5c] text-white placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>
          </div>

          {/* Right section with buttons */}
          <div className="flex items-center space-x-3">
            <AuthNav />
          </div>
        </div>
      </div>

      {/* Tabs navigation */}
      <div className="bg-white shadow-sm">
        <nav className="max-w-[1440px] mx-auto">
          <div className="flex justify-between">
            <div className="flex" aria-label="Tabs">
              {tabs.map((tab) => (
                <Link
                  key={tab.name}
                  href={tab.href}
                  className={`
                    px-4 py-3
                    text-sm font-medium whitespace-nowrap flex items-center
                    ${
                      (
                        tab.href === "/"
                          ? pathname === "/"
                          : pathname.startsWith(tab.href)
                      )
                        ? "bg-gray-100 text-gray-800"
                        : "text-gray-500 hover:text-gray-700 hover:bg-gray-50"
                    }
                  `}
                >
                  {tab.name}
                </Link>
              ))}
            </div>
          </div>
        </nav>
      </div>
    </>
  );
}
