"use client";

import React, {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useState,
} from "react";

import {
  getUserProfile,
  loginWithRedirect,
  logoutWithRedirect,
  userManager,
} from "@/lib/auth-oidc";

import { AuthContextType, UserProfile } from "./auth-context.type";

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const refreshUser = async () => {
    try {
      const userProfile = await getUserProfile();
      setUser(userProfile);
    } catch (error) {
      console.error("Error refreshing user:", error);
      setUser(null);
    }
  };

  const login = async (returnUrl = "/") => {
    await loginWithRedirect(returnUrl);
  };

  const logout = async () => {
    setUser(null);
    await logoutWithRedirect();
  };

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        await refreshUser();
      } catch (error) {
        console.error("Error initializing auth:", error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();

    if (userManager) {
      const handleUserLoaded = () => {
        refreshUser();
      };

      const handleUserUnloaded = () => {
        setUser(null);
      };

      const handleAccessTokenExpired = async () => {
        try {
          // The UserManager will automatically handle silent renewal due to automaticSilentRenew: true
          await refreshUser();
        } catch (error) {
          console.error(
            "Failed to refresh user after token expiration:",
            error,
          );
          try {
            if (userManager) {
              await userManager.removeUser();
            }
          } catch (removeError) {
            console.error(
              "Error removing user after token expiration failure:",
              removeError,
            );
          }
          // If silent renewal fails, the user might need to re-authenticate
          setUser(null);
        }
      };

      const handleSilentRenewError = async (error: Error) => {
        console.error("Silent renew error:", error);
        try {
          if (userManager) {
            await userManager.removeUser();
          }
        } catch (removeError) {
          console.error(
            "Error removing user after silent renew failure:",
            removeError,
          );
        }
        // Clear the user state to force re-authentication
        setUser(null);
      };

      userManager.events.addUserLoaded(handleUserLoaded);
      userManager.events.addUserUnloaded(handleUserUnloaded);
      userManager.events.addAccessTokenExpired(handleAccessTokenExpired);
      userManager.events.addSilentRenewError(handleSilentRenewError);

      return () => {
        if (userManager) {
          userManager.events.removeUserLoaded(handleUserLoaded);
          userManager.events.removeUserUnloaded(handleUserUnloaded);
          userManager.events.removeAccessTokenExpired(handleAccessTokenExpired);
          userManager.events.removeSilentRenewError(handleSilentRenewError);
        }
      };
    }
    return undefined;
  }, []);

  const value: AuthContextType = {
    isAuthenticated: !!user,
    isLoading,
    login,
    logout,
    refreshUser,
    user,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
