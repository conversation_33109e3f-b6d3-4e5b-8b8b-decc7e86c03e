export interface Price {
  quantity: number;
  unitPrice: number;
  currency: string;
  originalPrice?: number;
  originalCurrency?: string;
  conversionRate?: number;
}

export interface ProductSupply {
  manufacturer: string;
  minOrderQuantity: number;
  packaging: string | null;
  prices: Price[];
  sku: string;
  stock: number;
  supplier: string;
  supplierProductUrl: string;
  supplierDatasheetUrl: string;
  supplierImageUrl: string;
  authorized: boolean;
  leadTime: string;
}

export interface Product {
  supplies: ProductSupply[];
  description: string;
  leadTime: string;
  mpn: string;
}

export interface SearchProductsResponse {
  searchProducts: Product[];
}

export interface Manufacturer {
  id: string | number;
  name: string;
}

export interface Seller {
  id: string | number;
  z2_seller_id?: string | number | null;
  name: string;
  key?: string;
}

export interface SupplySeller {
  id: string | number;
  name: string;
  key: string;
}

export interface SupplyPrice {
  currency: string;
  quantity: number;
  unitPrice: number;
}

export interface Supply {
  id: string | number;
  part_id: string | number;
  seller_id: string | number;
  sku: string;
  packaging: string;
  min_order_quantity: number;
  stock: number;
  product_url: string;
  datasheet_url: string;
  lead_time: string;
  prices: SupplyPrice[];
  seller: SupplySeller;
  part?: Part;
}

export interface Part {
  id: string | number;
  z2_part_id: string | number;
  manufacture_id?: string | number;
  manufacturer_id?: string | number;
  mpn: string;
  category: string;
  subcategory0: string;
  subcategory1: string;
  pl_name: string;
  description: string;
  family_series: string;
  manufacturer: Manufacturer;
  supplies?: Supply[];
  lifecycle_status?: string | null;
  rohs_status?: string | null;
}

export enum MatchedStatus {
  Matched = "Matched",
  NotMatched = "NotMatched",
  SemiMatched = "SemiMatched",
  UserSelected = "UserSelected",
}

export interface Bom {
  id: string | number;
  name: string;
  project?: string;
  created_at: string;
  modified_at: string;
  bom_items?: BomItem[];
}

export interface BomItem {
  id: string | number;
  bom_id: string | number;
  matched_part_id?: string | number | null;
  supply_id?: string | number | null;
  mpn: string;
  manufacturer_name: string;
  matched_status: MatchedStatus | string;
  match_reason?: string;
  quantity: number;
  idx: number;
  created_at: string;
  modified_at: string;
  bom?: Bom | null;
  part?: Part | null;
  supply?: Supply | null;
}

export interface BomQueryResult {
  bom: Bom;
}

export interface BomsQueryResult {
  boms: Bom[];
}

export interface CreateBomItemMutationResult {
  createBomItem: {
    id: string | number;
    matched_part_id?: string | number | null;
    mpn: string;
    manufacturer_name: string;
    matched_status: MatchedStatus | string;
    idx: number;
  };
}

export interface UpdateBomItemMutationResult {
  updateBomItem: {
    id: string | number;
    matched_part_id?: string | number | null;
    mpn: string;
    manufacturer_name: string;
    matched_status: MatchedStatus | string;
    idx: number;
  };
}

export interface DeleteBomItemMutationResult {
  deleteBomItem: {
    bom_id: string | number;
  };
}

export interface DeleteBomMutationResult {
  deleteBom: {
    id: string;
  };
}

export interface CreateBomMutationResult {
  createBom: Bom;
}

export interface UpdateBomMutationResult {
  updateBom: {
    id: string;
    name: string;
    project: string;
  };
}

export interface ProjectsQueryResult {
  projects: string[];
}

// Products related types
export interface RawData {
  supplier: string;
  params:
    | {
        key: string;
        value: string;
      }[]
    | null;
  body: string | null;
  error: string | null;
}

export interface SearchProductResult {
  searchProductsWithRawData: {
    searchProducts: Product[];
    rawData: RawData[];
  };
}

export interface ManufacturerGroup {
  manufacturer: string;
  supplies: ProductSupply[];
}

export interface ProductGrouped {
  mpn: string;
  description: string;
  leadTime: string;
  manufacturerGroups: ManufacturerGroup[];
}

export interface SearchProductGroupedResult {
  searchProductsGroupedByManufacturer: {
    searchProducts: ProductGrouped[];
    rawData: RawData[];
  };
}

export interface LocalPart {
  id: string;
  z2_part_id: string;
  manufacture_id: string;
  mpn: string;
  category: string;
  subcategory0: string;
  subcategory1: string;
  pl_name: string;
  description: string;
  family_series: string;
  manufacturer: {
    id: string;
    name: string;
  };
}

export interface SearchLocalPartsResponse {
  searchLocalParts: LocalPart[];
}

export interface Parameter {
  ParameterId: number;
  ParameterText: string;
  ValueText: string;
}

export interface StandardPricing {
  BreakQuantity: number;
  UnitPrice: number;
  TotalPrice: number;
}

export interface ProductVariation {
  DigiKeyProductNumber: string;
  PackageType: {
    Id: number;
    Name: string;
  };
  StandardPricing: StandardPricing[];
  QuantityAvailableforPackageType: number;
  MinimumOrderQuantity: number;
  StandardPackage: number;
}

export interface ProductDetail {
  getDigiKeyProductDetail: {
    SearchLocaleUsed: {
      Site: string;
      Language: string;
      Currency: string;
    };
    Product: {
      Description: {
        ProductDescription: string;
        DetailedDescription: string;
      };
      Manufacturer: {
        Id: number;
        Name: string;
      };
      ManufacturerProductNumber: string;
      UnitPrice: number;
      ProductUrl: string;
      DatasheetUrl: string;
      PhotoUrl: string;
      ProductVariations: ProductVariation[];
      QuantityAvailable: number;
      ProductStatus: {
        Id: number;
        Status: string;
      };
      Parameters: Parameter[];
      Series: {
        Id: number;
        Name: string;
      };
      Classifications: {
        ReachStatus: string;
        RohsStatus: string;
      };
    };
  };
}

export enum ExpirationStatus {
  Active = "Active",
  Error = "Error",
  Expired = "Expired",
}

export interface DigiKeyQuote {
  TotalProducts: number | null;
  QuoteId: number;
  CustomerId: number;
  DateCreated: string;
  ExpirationStatus: ExpirationStatus;
  Currency: string;
  QuoteName: string;
}

export interface DigiKeyQuotesResponse {
  getDigiKeyQuotes: {
    TotalQuotes: number;
    Quotes: DigiKeyQuote[];
  };
}

export interface Quantity {
  Quantity: number;
  UnitPrice: number;
  ExtendedPrice: number;
}

export interface QuoteProduct {
  DetailId: number;
  DigikeyProductNumber: string;
  RequestedProductNumber: string;
  ManufacturerProductNumber: string;
  ManufacturerName: string;
  Description: string;
  CustomerReference: string;
  CountryOfOrigin: string;
  PackageType: string;
  MinimumOrderQuantity: number;
  QuantityAvailable: number;
  IsObsolete: boolean;
  IsDiscontinued: boolean;
  IsMfgQuoteRequired: boolean;
  IsMarketplace: boolean;
  RoHSStatus: string;
  ReachStatus: string;
  StandardPackage: string;
  ExpirationDate: string;
  Quantities: Quantity[];
}

export interface GetProductsFromQuoteResponse {
  getProductsFromQuote: {
    QuoteProducts: QuoteProduct[];
  };
}

// Supplier Preferences types
export interface SupplierConfig {
  apiKey?: string;
  baseUrl?: string;
  secret?: string;
  client?: string;
  cookie?: string;
  licenseKey?: string;
}

export interface SupplierPreference {
  seller_id: string;
  seller_key: string;
  enabled: boolean;
  id: string;
  config?: SupplierConfig | null;
}

export interface UpdateSupplierPreferenceInput {
  seller_key: string;
  enabled?: boolean;
  config?: SupplierConfig;
}

export interface GetSupplierPreferencesResult {
  supplierPreferences: SupplierPreference[];
}

export interface UpdateSupplierPreferenceResult {
  updateSupplierPreference: SupplierPreference;
}

export interface SuppliesByPartIdResponse {
  suppliesByPartId: Supply[];
}
