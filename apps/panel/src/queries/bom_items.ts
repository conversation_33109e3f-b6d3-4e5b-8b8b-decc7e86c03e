import { gql } from "@apollo/client";

import {
  Bom<PERSON>tem,
  BomQueryResult,
  CreateBomItemMutationResult,
  DeleteBomItemMutationResult,
  MatchedStatus,
  Supply,
  SupplyPrice,
  UpdateBomItemMutationResult,
} from "./types";

export type {
  Bom<PERSON>tem,
  BomQueryResult,
  CreateBomItemMutationResult,
  DeleteBomItemMutationResult,
  Supply,
  SupplyPrice,
  UpdateBomItemMutationResult,
};
export { MatchedStatus };

export const GET_BOM = gql`
  query Bom($bomId: Int!) {
    bom(id: $bomId) {
      id
      name
      project
      created_at
      modified_at
      bom_items {
        id
        bom_id
        matched_part_id
        supply_id
        mpn
        manufacturer_name
        matched_status
        match_reason
        quantity
        idx
        created_at
        modified_at
        part {
          id
          z2_part_id
          manufacture_id
          mpn
          category
          subcategory0
          subcategory1
          pl_name
          description
          family_series
          manufacturer {
            id
            name
          }
          lifecycle_status
          rohs_status
          supplies {
            id
            part_id
            seller_id
            sku
            packaging
            min_order_quantity
            stock
            product_url
            datasheet_url
            lead_time
            prices
            seller {
              id
              name
              key
            }
            part {
              id
              z2_part_id
              manufacture_id
              mpn
              category
              subcategory0
              subcategory1
              pl_name
              description
              family_series
              manufacturer {
                id
                name
              }
            }
          }
        }
        supply {
          id
          part_id
          seller_id
          sku
          packaging
          min_order_quantity
          stock
          product_url
          datasheet_url
          lead_time
          prices
          seller {
            id
            name
            key
          }
          part {
            id
            z2_part_id
            manufacture_id
            mpn
            category
            subcategory0
            subcategory1
            pl_name
            description
            family_series
            lifecycle_status
            rohs_status
            manufacturer {
              id
              name
            }
          }
        }
      }
    }
  }
`;

export const CREATE_BOM_ITEM = gql`
  mutation CreateBomItem($createBomItemInput: CreateBomItemInput!) {
    createBomItem(createBomItemInput: $createBomItemInput) {
      id
      matched_part_id
      mpn
      manufacturer_name
      matched_status
      idx
    }
  }
`;

export const UPDATE_BOM_ITEM = gql`
  mutation UpdateBomItem($updateBomItemInput: UpdateBomItemInput!) {
    updateBomItem(updateBomItemInput: $updateBomItemInput) {
      id
      matched_part_id
      mpn
      manufacturer_name
      matched_status
      idx
    }
  }
`;

export const DELETE_BOM_ITEM = gql`
  mutation DeleteBomItem($deleteBomItemId: Int!) {
    deleteBomItem(id: $deleteBomItemId) {
      bom_id
    }
  }
`;
