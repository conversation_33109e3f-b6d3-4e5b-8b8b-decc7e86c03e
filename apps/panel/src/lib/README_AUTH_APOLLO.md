# Apollo Client Authentication Integration

## Overview

This document describes the authentication integration implemented in the Apollo GraphQL client for the panel application.

## Implementation

### Enhanced Apollo Client (`apolloClient.tsx`)

The Apollo client has been enhanced to automatically include authentication headers in all GraphQL requests:

```typescript
// Create the auth link that adds authentication headers
const authLink = setContext(async (_, { headers }) => {
  try {
    // Get authentication headers (includes Bearer token if user is authenticated)
    const authHeaders = await getAuthHeaders();
    
    return {
      headers: {
        ...headers,
        ...authHeaders,
      },
    };
  } catch (error) {
    // If auth fails, continue without auth headers
    console.warn("Failed to get auth headers:", error);
    return {
      headers: {
        ...headers,
      },
    };
  }
});

// Chain the auth link with the HTTP link
link: authLink.concat(httpLink)
```

### Key Features

1. **Automatic Authentication**: All GraphQL queries and mutations automatically include authentication headers when a user is logged in.

2. **Graceful Degradation**: If authentication fails or user is not logged in, requests continue without auth headers.

3. **Error Handling**: Authentication errors are logged but don't break the GraphQL requests.

4. **Development Logging**: In development mode, logs whether requests are made with or without auth tokens.

### Authentication Flow

1. User authenticates via Z2Data SSO (OIDC flow)
2. JWT token is stored in browser localStorage
3. Apollo client automatically retrieves token via `getAuthHeaders()`
4. Token is included as `Authorization: Bearer <token>` header
5. API-Service receives authenticated requests

### Dependencies

- `@apollo/client` - Apollo GraphQL client
- `@apollo/client/link/context` - Context link for adding headers
- `./auth-oidc.utils` - Authentication utilities

### Testing

A test page has been created at `/auth-test` to verify the authentication integration:

- Shows current authentication status
- Displays auth headers being sent
- Tests a GraphQL query with authentication
- Provides debugging information

### Usage

No changes are required for existing GraphQL queries. All `useQuery`, `useLazyQuery`, and `useMutation` hooks will automatically include authentication headers when available.

Example:
```typescript
// This query will automatically include auth headers if user is authenticated
const { loading, error, data } = useQuery(GET_QUOTES);
```

### Environment Variables

The Apollo client uses the following environment variable:
- `NEXT_PUBLIC_API_BASE` - Base URL for the API service GraphQL endpoint

### Next Steps

This implementation completes Phase 1 of the authentication integration. The next phase involves implementing JWT validation in the api-service to handle these authenticated requests.
