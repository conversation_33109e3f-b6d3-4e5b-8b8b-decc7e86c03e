import { Test, TestingModule } from '@nestjs/testing';
import { PartService } from './part.service';
import { PartRepository } from './part.repo';
import { PartAlternativeRepo } from './part-alternative.repo';
import { Z2DataService } from '../z2data/z2data.service';
import { ManufacturerNormalizationService } from '../manufacturer/manufacturer-normalization.service';
import { PrismaService } from '../prisma.service';

describe('PartService - Enhanced Alternative Part Caching', () => {
  let service: PartService;
  let partRepo: PartRepository;
  let partAlternativeRepo: PartAlternativeRepo;
  let z2dataService: Z2DataService;

  const mockPart = {
    id: 1,
    z2_part_id: 123,
    manufacture_id: 1,
    mpn: 'TEST-MPN-001',
    category: null,
    subcategory0: null,
    subcategory1: null,
    pl_name: 'Test Product',
    description: 'Test Description',
    family_series: null,
    lifecycle_status: 'Active',
    rohs_status: 'Compliant',
    last_z2data_update: new Date(),
  };

  const mockZ2CrossResponse = {
    statusCode: 200,
    status: 'OK',
    results: {
      companyName: 'Test Company',
      dataSheet: 'http://test.com/datasheet',
      partNumber: 'TEST-MPN-001',
      numFound: 5,
      pageNumber: 1,
      partLifecycle: 'Active',
      roHsFlag: 'Yes',
      crossesDetails: {
        Total_Crosses_Found: 3,
        crosses: [
          {
            companyName: 'Alternative Company 1',
            crossComment: 'Direct replacement',
            crossType: 'Exact',
            dataSheet: 'http://alt1.com/datasheet',
            partNumber: 'ALT-MPN-001',
            package: 'SOT-23',
            partDescription: 'Alternative part 1',
            partLifecycle: 'Active',
            roHsFlag: 'Yes',
          },
          {
            companyName: 'Alternative Company 2',
            crossComment: 'Functional equivalent',
            crossType: 'Similar',
            dataSheet: 'http://alt2.com/datasheet',
            partNumber: 'ALT-MPN-002',
            package: 'SOT-23',
            partDescription: 'Alternative part 2',
            partLifecycle: 'Active',
            roHsFlag: 'Yes',
          },
        ],
      },
    },
  };

  const mockAlternativePart = {
    id: 2,
    z2_part_id: 456,
    manufacture_id: 2,
    mpn: 'ALT-MPN-001',
    category: null,
    subcategory0: null,
    subcategory1: null,
    pl_name: 'Alternative Product',
    description: 'Alternative Description',
    family_series: null,
    lifecycle_status: 'Active',
    rohs_status: 'Compliant',
    last_z2data_update: new Date(),
    manufacturer: {
      id: 2,
      name: 'Alternative Company 1',
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PartService,
        {
          provide: PartRepository,
          useValue: {
            findById: jest.fn(),
            findByMpnAndManufacturer: jest.fn(),
            findOrCreateManufacturer: jest.fn(),
            create: jest.fn(),
            update: jest.fn(),
          },
        },
        {
          provide: PartAlternativeRepo,
          useValue: {
            findCachedAlternatives: jest.fn(),
            upsertAlternatives: jest.fn(),
            findAlternativePartLocally: jest.fn(),
          },
        },
        {
          provide: Z2DataService,
          useValue: {
            getCrossDataByPartId: jest.fn(),
            validateParts: jest.fn(),
          },
        },
        {
          provide: ManufacturerNormalizationService,
          useValue: {
            normalizeManufacturer: jest.fn(),
          },
        },
        {
          provide: PrismaService,
          useValue: {},
        },
      ],
    }).compile();

    service = module.get<PartService>(PartService);
    partRepo = module.get<PartRepository>(PartRepository);
    partAlternativeRepo = module.get<PartAlternativeRepo>(PartAlternativeRepo);
    z2dataService = module.get<Z2DataService>(Z2DataService);
  });

  describe('getPartAlternatives with enhanced caching', () => {
    it('should find local alternative parts and map them correctly', async () => {
      // Setup mocks
      jest.spyOn(partRepo, 'findById').mockResolvedValue(mockPart as any);
      jest.spyOn(partAlternativeRepo, 'findCachedAlternatives').mockResolvedValue(null);
      jest.spyOn(z2dataService, 'getCrossDataByPartId').mockResolvedValue(mockZ2CrossResponse as any);
      
      // Mock finding the first alternative part locally
      jest.spyOn(partAlternativeRepo, 'findAlternativePartLocally')
        .mockResolvedValueOnce(mockAlternativePart as any) // First call finds local part
        .mockResolvedValueOnce(null); // Second call doesn't find local part

      // Mock the validation for the second part (not found locally)
      jest.spyOn(service, 'findPartByMpnAndManufacturer').mockResolvedValue([
        { id: 3, mpn: 'ALT-MPN-002' } as any,
        'Matched' as any,
        'Created from Z2Data' as any,
      ]);

      jest.spyOn(partAlternativeRepo, 'upsertAlternatives').mockResolvedValue();

      // Execute
      const result = await service.getPartAlternatives(1);

      // Verify
      expect(result).toBeDefined();
      expect(result?.alternatives).toHaveLength(2);
      expect(partAlternativeRepo.findAlternativePartLocally).toHaveBeenCalledTimes(2);
      expect(partAlternativeRepo.findAlternativePartLocally).toHaveBeenCalledWith(
        'ALT-MPN-001',
        'Alternative Company 1'
      );
      expect(partAlternativeRepo.findAlternativePartLocally).toHaveBeenCalledWith(
        'ALT-MPN-002',
        'Alternative Company 2'
      );
      expect(service.findPartByMpnAndManufacturer).toHaveBeenCalledWith(
        'ALT-MPN-002',
        'Alternative Company 2'
      );
      expect(partAlternativeRepo.upsertAlternatives).toHaveBeenCalledWith(
        1,
        123,
        mockZ2CrossResponse,
        expect.arrayContaining([
          expect.objectContaining({
            partNumber: 'ALT-MPN-001',
            localPartId: 2,
          }),
          expect.objectContaining({
            partNumber: 'ALT-MPN-002',
            localPartId: 3,
          }),
        ])
      );
    });

    it('should handle cases where no local alternatives are found', async () => {
      // Setup mocks
      jest.spyOn(partRepo, 'findById').mockResolvedValue(mockPart as any);
      jest.spyOn(partAlternativeRepo, 'findCachedAlternatives').mockResolvedValue(null);
      jest.spyOn(z2dataService, 'getCrossDataByPartId').mockResolvedValue(mockZ2CrossResponse as any);
      jest.spyOn(partAlternativeRepo, 'findAlternativePartLocally').mockResolvedValue(null);
      jest.spyOn(service, 'findPartByMpnAndManufacturer').mockResolvedValue([null, 'NotMatched' as any, 'No match found']);
      jest.spyOn(partAlternativeRepo, 'upsertAlternatives').mockResolvedValue();

      // Execute
      const result = await service.getPartAlternatives(1);

      // Verify
      expect(result).toBeDefined();
      expect(result?.alternatives).toHaveLength(2);
      expect(partAlternativeRepo.upsertAlternatives).toHaveBeenCalledWith(
        1,
        123,
        mockZ2CrossResponse,
        expect.arrayContaining([
          expect.objectContaining({
            partNumber: 'ALT-MPN-001',
            localPartId: undefined,
          }),
          expect.objectContaining({
            partNumber: 'ALT-MPN-002',
            localPartId: undefined,
          }),
        ])
      );
    });
  });
});
