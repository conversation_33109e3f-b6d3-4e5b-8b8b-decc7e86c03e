export interface CreatePartInput {
  manufacture_id: number;
  mpn: string;
  category?: string;
  subcategory0?: string;
  subcategory1?: string;
  pl_name?: string;
  description?: string;
  family_series?: string;
  z2_part_id?: number;
}

export interface UpdatePartInput {
  id: number;
  mpn?: string;
  category?: string;
  subcategory0?: string;
  subcategory1?: string;
  pl_name?: string | null;
  description?: string | null;
  family_series?: string;
  z2_part_id?: number;
  lifecycle_status?: string;
  rohs_status?: string;
  last_z2data_update?: Date;
  market_status?: string;
  number_of_seller?: number;
  total_quantity_available?: number;
  lowest_price?: number;
  min_lead_time_weeks?: number;
  max_lead_time_weeks?: number;
}

export interface SearchPartInput {
  keyword: string;
  limit?: number;
}
