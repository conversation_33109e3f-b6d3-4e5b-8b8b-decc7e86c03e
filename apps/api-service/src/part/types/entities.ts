import { Supply } from "../../common/types";
import {
  manufacturer as PrismaManufacturer,
  part as PrismaPart,
} from "../../generated/client";

export interface Part extends PrismaPart {
  manufacturer?: PrismaManufacturer;
  supplies?: Supply[];
}

export interface PartAlternative {
  mpn: string;
  companyName: string;
  dataSheet: string;
  crossType: string;
  crossComment: string;
  partDescription: string;
  partLifecycle: string;
  roHsFlag?: string;
  package: string;
  part?: Part;
}
