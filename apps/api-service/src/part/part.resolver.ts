import { UseGuards } from "@nestjs/common";
import { Args, Query, Resolver } from "@nestjs/graphql";

import { CurrentUser } from "../auth/decorators/current-user.decorator";
import { OptionalAuthGuard } from "../auth/guards/optional-auth.guard";
import type { AuthUser } from "../auth/interfaces/auth-user.interface";
import { PartService } from "./part.service";

@Resolver("Part")
@UseGuards(OptionalAuthGuard)
export class PartResolver {
  constructor(private readonly partService: PartService) {}

  @Query("searchLocalParts")
  findByKeyword(
    @Args("keyword") keyword: string,
    @Args("limit", { nullable: true }) limit?: number,
    @Args("forceUpdate", { nullable: true }) forceUpdate: boolean = false,
    @CurrentUser() user?: AuthUser,
  ) {
    return this.partService.findByKeyword(keyword, limit, false, forceUpdate);
  }

  @Query("getPartAlternatives")
  async getPartAlternatives(
    @Args("partId") partId: number,
    @CurrentUser() user?: AuthUser,
  ) {
    return this.partService.getPartAlternatives(partId);
  }
}
