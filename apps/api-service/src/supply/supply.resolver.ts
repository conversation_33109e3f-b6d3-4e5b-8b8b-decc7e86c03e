import { Injectable, UseGuards } from "@nestjs/common";
import { Args, Query, Resolver } from "@nestjs/graphql";

import { CurrentUser } from "../auth/decorators/current-user.decorator";
import { OptionalAuthGuard } from "../auth/guards/optional-auth.guard";
import type { AuthUser } from "../auth/interfaces/auth-user.interface";
import { SupplyService } from "./supply.service";

@Resolver("Supply")
@UseGuards(OptionalAuthGuard)
@Injectable()
export class SupplyResolver {
  constructor(
    private readonly supplyService: SupplyService,
  ) {}

  @Query("suppliesByMpn")
  async suppliesByMpn(
    @Args("mpn") mpn: string,
    @CurrentUser() user?: AuthUser,
  ) {
    return this.supplyService.findSuppliesByMpn(mpn);
  }

  @Query("suppliesByPartId")
  async suppliesByPartId(
    @Args("partId") partId: number,
    @CurrentUser() user?: AuthUser,
  ) {
    return this.supplyService.findDetailedSuppliesByPartId(partId);
  }
}
