import { UseGuards } from "@nestjs/common";
import { Args, Info, Mutation, Query, Resolver } from "@nestjs/graphql";
import type { GraphQLResolveInfo } from "graphql";

import { CurrentUser } from "../auth/decorators/current-user.decorator";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import type { AuthUser } from "../auth/interfaces/auth-user.interface";
import { BomService } from "./bom.service";
import type {
  CreateBomInput,
  CreateBomItemInput,
  UpdateBomInput,
  UpdateBomItemInput,
} from "./types";

@Resolver("Bom")
@UseGuards(JwtAuthGuard)
export class BomResolver {
  constructor(private readonly bomService: BomService) {}

  @Query("boms")
  findAllBoms(
    @Info() info: GraphQLResolveInfo,
    @CurrentUser() user: AuthUser,
  ) {
    const includeBomItems = this.fieldWasRequested("bom_items", info);
    const includeSupplies = this.fieldWasRequested("supply", info);
    return this.bomService.findAllBoms(includeBomItems, includeSupplies);
  }

  @Query("bom")
  findBomById(
    @Args("id") id: number,
    @Info() info: GraphQLResolveInfo,
    @CurrentUser() user: AuthUser,
  ) {
    const includeBomItems = this.fieldWasRequested("bom_items", info);
    const includeSupplies = this.deepNestedFieldWasRequested(
      ["bom_items", "part", "supplies"],
      info,
    );

    return this.bomService.findBomById(id, includeBomItems, includeSupplies);
  }

  // BOM Mutations
  @Mutation("createBom")
  createBom(@Args("createBomInput") createBomInput: CreateBomInput) {
    return this.bomService.createBom(createBomInput);
  }

  @Mutation("updateBom")
  updateBom(@Args("updateBomInput") updateBomInput: UpdateBomInput) {
    return this.bomService.updateBom(updateBomInput);
  }

  @Mutation("deleteBom")
  deleteBom(@Args("id") id: number) {
    return this.bomService.deleteBom(id);
  }

  // BOM Item Queries
  @Query("bomItems")
  findAllBomItems(@Args("bomId") bomId: number) {
    return this.bomService.findAllBomItems(bomId);
  }

  @Query("bomItem")
  findBomItemById(@Args("id") id: number) {
    return this.bomService.findBomItemById(id);
  }

  @Query("projects")
  findAllProjects(
    @Args("search", { nullable: true }) search?: string,
    @Args("limit", { nullable: true }) limit: number = 5,
  ) {
    return this.bomService.findAllProjects(search, limit);
  }

  // BOM Item Mutations
  @Mutation("createBomItem")
  createBomItem(
    @Args("createBomItemInput") createBomItemInput: CreateBomItemInput,
  ) {
    return this.bomService.createBomItem(createBomItemInput, true);
  }

  @Mutation("updateBomItem")
  updateBomItem(
    @Args("updateBomItemInput") updateBomItemInput: UpdateBomItemInput,
  ) {
    return this.bomService.updateBomItem(updateBomItemInput);
  }

  @Mutation("deleteBomItem")
  deleteBomItem(@Args("id") id: number) {
    return this.bomService.deleteBomItem(id);
  }

  @Mutation("changeMatchedPart")
  changeMatchedPart(
    @Args("bomItemId") bomItemId: bigint,
    @Args("newPartId") newPartId: number,
  ) {
    return this.bomService.changeMatchedPart(bomItemId, newPartId);
  }

  private fieldWasRequested(
    fieldName: string,
    info: GraphQLResolveInfo,
  ): boolean {
    const selections = info.fieldNodes[0].selectionSet?.selections || [];
    return selections.some(
      (selection) =>
        selection.kind === "Field" && selection.name.value === fieldName,
    );
  }
  private deepNestedFieldWasRequested(
    fieldPath: string[],
    info: GraphQLResolveInfo,
  ): boolean {
    let currentSelections = info.fieldNodes[0].selectionSet?.selections || [];

    for (const fieldName of fieldPath) {
      const field = currentSelections.find(
        (selection) =>
          selection.kind === "Field" && selection.name.value === fieldName,
      );

      if (!field || field.kind !== "Field" || !field.selectionSet) {
        return false;
      }

      currentSelections = field.selectionSet.selections || [];
    }

    return true;
  }
}
