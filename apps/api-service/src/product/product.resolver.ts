import { Logger, UseGuards } from "@nestjs/common";
import { Args, Query, Resolver } from "@nestjs/graphql";

import { CurrentUser } from "../auth/decorators/current-user.decorator";
import { OptionalAuthGuard } from "../auth/guards/optional-auth.guard";
import type { AuthUser } from "../auth/interfaces/auth-user.interface";
import type { SupplierConfigInput } from "../common/types";
import { ProductService } from "./product.service";

@Resolver("Product")
@UseGuards(OptionalAuthGuard)
export class ProductResolver {
  private readonly logger = new Logger(ProductResolver.name);

  constructor(private readonly productService: ProductService) {}

  @Query("searchProducts")
  async searchProducts(
    @Args("keyword") keyword: string,
    @Args("limit") limit: number = 5,
    @CurrentUser() user?: AuthUser,
  ) {
    try {
      if (user) {
        this.logger.log(`User ${user.email} searching products: ${keyword}`);
      } else {
        this.logger.log(`Anonymous user searching products: ${keyword}`);
      }
      return await this.productService.searchProducts2(keyword, limit);
    } catch (error: unknown) {
      this.logger.error(
        `Product search failed: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
      throw new Error(
        `Failed to search products: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
    }
  }

  @Query("searchProductsWithRawData")
  async searchProductsWithRawData(
    @Args("keyword") keyword: string,
    @Args("limit") limit: number = 5,
    @Args("targetCurrency") targetCurrency: string = "USD",
    @Args("sellerKeys") sellerKeys?: string[],
  ) {
    try {
      return await this.productService.searchProductsWithCurrencyAndRawData(
        keyword,
        limit,
        targetCurrency.toUpperCase(),
        sellerKeys,
      );
    } catch (error: unknown) {
      this.logger.error(
        `Product search with currency conversion failed: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
      throw new Error(
        `Failed to search products with currency conversion: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
    }
  }

  @Query("searchProductsWithRawDataAndConfig")
  async searchProductsWithRawDataAndConfig(
    @Args("keyword") keyword: string,
    @Args("limit") limit: number = 5,
    @Args("sellerKey") sellerKey: string,
    @Args("config") config: SupplierConfigInput,
  ) {
    try {
      if (!config) {
        throw new Error("Supplier configuration is required");
      }
      return await this.productService.searchProductsWithRawDataAndConfig(
        keyword,
        limit,
        sellerKey,
        config,
      );
    } catch (error: unknown) {
      this.logger.error(
        `Product search with config failed: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
      throw new Error(
        `Failed to search products with config: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
    }
  }

  @Query("searchProductsGroupedByManufacturer")
  async searchProductsGroupedByManufacturer(
    @Args("keyword") keyword: string,
    @Args("limit") limit: number = 5,
    @Args("targetCurrency") targetCurrency: string = "USD",
    @Args("sellerKeys") sellerKeys?: string[],
  ) {
    try {
      return await this.productService.searchProductsGroupedByManufacturer(
        keyword,
        limit,
        targetCurrency.toUpperCase(),
        sellerKeys,
      );
    } catch (error: unknown) {
      this.logger.error(
        `Product search grouped by manufacturer failed: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
      throw new Error(
        `Failed to search products grouped by manufacturer: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
    }
  }
}
