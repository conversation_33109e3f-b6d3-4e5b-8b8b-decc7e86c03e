import { Args, Query, Resolver } from "@nestjs/graphql";

import { Public } from "../auth/decorators/public.decorator";
import { CurrencyService } from "./currency.service";

@Resolver("Currency")
@Public()
export class CurrencyResolver {
  constructor(private readonly currencyService: CurrencyService) {}

  @Query("exchangeRate")
  async exchangeRate(@Args("from") from: string, @Args("to") to: string) {
    const { rate, lastUpdated } = await this.currencyService.getExchangeRate(from, to);
    return {
      base: from.toUpperCase(),
      lastUpdated: lastUpdated.toISOString(),
      rate,
      target: to.toUpperCase(),
    };
  }

  @Query("convertCurrency")
  async convertCurrency(
    @Args("amount") amount: number,
    @Args("from") from: string,
    @Args("to") to: string,
  ) {
    const { convertedAmount, rate, lastUpdated } = await this.currencyService.convertAmount(
      amount,
      from,
      to,
    );
    
    return {
      amount,
      convertedAmount,
      fromCurrency: from.toUpperCase(),
      lastUpdated: lastUpdated.toISOString(),
      rate,
      toCurrency: to.toUpperCase(),
    };
  }

  @Query("availableCurrencies")
  async availableCurrencies() {
    return await this.currencyService.getAvailableCurrencies();
  }
}