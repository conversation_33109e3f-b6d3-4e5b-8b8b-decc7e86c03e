import { Mo<PERSON><PERSON> } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';

import { PrismaModule } from '../prisma/prisma.module';
import { AuthService } from './auth.service';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { OptionalAuthGuard } from './guards/optional-auth.guard';
import { JwtStrategy } from './strategies/jwt.strategy';

@Module({
  imports: [
    PassportModule.register({ defaultStrategy: 'jwt' }),
    JwtModule.register({
      // We don't need to configure JWT signing since we're only validating tokens
      // The JWT strategy will handle token validation using JWKS
    }),
    PrismaModule,
  ],
  providers: [
    AuthService,
    JwtStrategy,
    JwtAuthGuard,
    OptionalAuthGuard,
  ],
  exports: [
    AuthService,
    JwtAuthGuard,
    OptionalAuthGuard,
    PassportModule,
    JwtModule,
  ],
})
export class AuthModule {}
