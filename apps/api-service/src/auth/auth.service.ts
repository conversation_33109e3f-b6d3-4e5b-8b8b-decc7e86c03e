import { Injectable, Logger } from '@nestjs/common';

import { PrismaService } from '../prisma.service';
import { AuthUser } from './interfaces/auth-user.interface';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(private readonly prisma: PrismaService) {}

  async findOrCreateUser(authUser: AuthUser) {
    try {
      // First, try to find the user by Z2Data ID
      let user = await this.prisma.user.findUnique({
        where: { z2dataId: authUser.id },
      });

      if (!user) {
        // If user doesn't exist, create a new one
        this.logger.log(`Creating new user for Z2Data ID: ${authUser.id}`);
        user = await this.prisma.user.create({
          data: {
            z2dataId: authUser.id,
            email: authUser.email,
            name: authUser.name,
            role: authUser.roles,
          },
        });
        this.logger.log(`User created successfully: ${user.email}`);
      } else {
        // Update user information if it has changed
        const needsUpdate = 
          user.email !== authUser.email ||
          user.name !== authUser.name ||
          JSON.stringify(user.role) !== JSON.stringify(authUser.roles);

        if (needsUpdate) {
          this.logger.log(`Updating user information for: ${authUser.email}`);
          user = await this.prisma.user.update({
            where: { id: user.id },
            data: {
              email: authUser.email,
              name: authUser.name,
              role: authUser.roles,
            },
          });
        }
      }

      return user;
    } catch (error) {
      this.logger.error(`Failed to find or create user: ${error instanceof Error ? error.message : String(error)}`, error instanceof Error ? error.stack : undefined);
      throw error;
    }
  }

  async getUserById(userId: string) {
    try {
      return await this.prisma.user.findUnique({
        where: { id: userId },
      });
    } catch (error) {
      this.logger.error(`Failed to get user by ID: ${error instanceof Error ? error.message : String(error)}`, error instanceof Error ? error.stack : undefined);
      throw error;
    }
  }

  async getUserByZ2DataId(z2dataId: string) {
    try {
      return await this.prisma.user.findUnique({
        where: { z2dataId },
      });
    } catch (error) {
      this.logger.error(`Failed to get user by Z2Data ID: ${error instanceof Error ? error.message : String(error)}`, error instanceof Error ? error.stack : undefined);
      throw error;
    }
  }
}
