import { ExecutionContext, Injectable, Logger } from '@nestjs/common';

import { JwtAuthGuard } from './jwt-auth.guard';

@Injectable()
export class OptionalAuthGuard extends JwtAuthGuard {
  private readonly optionalLogger = new Logger(OptionalAuthGuard.name);

  override handleRequest(err: any, user: any, info: any, context: ExecutionContext) {
    // If authentication fails, return null instead of throwing an error
    if (err || !user) {
      this.optionalLogger.debug(`Optional authentication failed, proceeding without user: ${err?.message || info?.message || 'No token provided'}`);
      return null;
    }

    this.optionalLogger.debug(`Optional authentication successful for user: ${user.email}`);
    return user;
  }
}
