import { Injectable, Logger } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { passportJwtSecret } from 'jwks-rsa';

import { AuthUser } from '../interfaces/auth-user.interface';
import { JwtPayload } from '../interfaces/jwt-payload.interface';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  private readonly logger = new Logger(JwtStrategy.name);

  constructor() {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKeyProvider: passportJwtSecret({
        cache: true,
        rateLimit: true,
        jwksRequestsPerMinute: 5,
        jwksUri: process.env.Z2DATA_SSO_JWKS_URI || 'https://testsso.z2data.com/.well-known/jwks',
      }),
      audience: process.env.Z2DATA_SSO_AUDIENCE || 'api.z2data.com',
      issuer: process.env.Z2DATA_SSO_AUTHORITY || 'https://testsso.z2data.com',
      algorithms: ['RS256'],
    });
  }

  async validate(payload: JwtPayload): Promise<AuthUser> {
    this.logger.debug(`Validating JWT payload for user: ${payload.sub}`);
    
    const user: AuthUser = {
      id: payload.sub,
      email: payload.email,
      name: payload.name,
      roles: payload.role || ['FREE'],
    };

    this.logger.debug(`JWT validation successful for user: ${user.email}`);
    return user;
  }
}
