import { Logger, UseGuards } from "@nestjs/common";
import { Args, Mutation, Query, Resolver } from "@nestjs/graphql";

import { CurrentUser } from "../../auth/decorators/current-user.decorator";
import { JwtAuthGuard } from "../../auth/guards/jwt-auth.guard";
import type { AuthUser } from "../../auth/interfaces/auth-user.interface";
import { DigiKeyService } from "./digikey.service";
import { ProductSearchResponse, ProductsFromQuoteResponse } from "./types";

@Resolver("DigiKey")
export class DigiKeyResolver {
  private readonly logger = new Logger(DigiKeyResolver.name);

  constructor(private readonly digikeyService: DigiKeyService) {}

  @UseGuards(JwtAuthGuard)
  @Query("searchDigiKeyProducts")
  async searchDigiKeyProducts(
    @Args("keyword") keyword: string,
    @Args("limit") limit: number = 2,
    @CurrentUser() user: AuthUser,
  ): Promise<ProductSearchResponse> {
    try {
      this.logger.log(`User ${user.email} searching DigiKey products: ${keyword}`);
      const response = await this.digikeyService.searchProducts(keyword, limit);
      return response;
    } catch (error) {
      this.logger.error(
        `Search failed for user ${user.email}: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  @UseGuards(JwtAuthGuard)
  @Query("getDigiKeyProductDetail")
  async getDigiKeyProductDetail(
    @Args("mpn") mpn: string,
    @CurrentUser() user: AuthUser,
  ) {
    try {
      this.logger.log(`User ${user.email} requesting DigiKey product detail: ${mpn}`);
      return await this.digikeyService.productDetail(mpn);
    } catch (error) {
      this.logger.error(
        `Failed to retrieve product detail for user ${user.email}: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  @UseGuards(JwtAuthGuard)
  @Query("getDigiKeyQuotes")
  async getDigiKeyQuotes(
    @Args("offset") offset: number = 0,
    @Args("limit") limit: number = 10,
    @CurrentUser() user: AuthUser,
  ) {
    try {
      this.logger.log(`User ${user.email} requesting DigiKey quotes`);
      return await this.digikeyService.getQuotes(offset, limit);
    } catch (error) {
      this.logger.error(
        `Failed to retrieve quotes for user ${user.email}: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  @UseGuards(JwtAuthGuard)
  @Query("getProductsFromQuote")
  async getProductsFromQuote(
    @Args("quoteId") quoteId: number,
    @Args("offset") offset: number = 0,
    @Args("limit") limit: number = 10,
    @CurrentUser() user: AuthUser,
  ): Promise<ProductsFromQuoteResponse> {
    try {
      this.logger.log(`User ${user.email} requesting products from quote ${quoteId}`);
      return await this.digikeyService.getProductsFromQuote(
        quoteId,
        offset,
        limit,
      );
    } catch (error) {
      this.logger.error(
        `Failed to retrieve products from quote for user ${user.email}: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }

  @UseGuards(JwtAuthGuard)
  @Mutation("createQuoteWithItem")
  async createQuoteWithItem(
    @Args("partNumber") partNumber: string,
    @Args("quantity") quantity: number,
    @CurrentUser() user: AuthUser,
  ) {
    try {
      this.logger.log(`User ${user.email} creating quote with item: ${partNumber}`);
      return await this.digikeyService.createQuoteWithItem(
        partNumber,
        quantity,
      );
    } catch (error) {
      this.logger.error(
        `Failed to create quote with item for user ${user.email}: ${error instanceof Error ? error.message : String(error)}`,
      );
      throw error;
    }
  }
}
