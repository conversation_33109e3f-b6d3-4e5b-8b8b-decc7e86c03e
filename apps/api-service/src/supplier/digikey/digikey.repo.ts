import { Injectable } from "@nestjs/common";

import { DigikeyAuthMeta } from "../../metadata/metadata.model";
import { MetadataService } from "../../metadata/metadata.service";

@Injectable()
export class DigiKeyRepository {
  private readonly DIGIKEY_AUTH_KEY = "digikey_auth";
  // Legacy fallback for backward compatibility
  private readonly LEGACY_USER_ID = "1";

  constructor(private readonly metadataService: MetadataService) {}

  async saveAuthMetadata(authMeta: DigikeyAuthMeta, userId?: string): Promise<boolean> {
    const userIdToUse = userId || this.LEGACY_USER_ID;
    const result = await this.metadataService.setUserMetaData(
      userIdToUse,
      this.DIGIKEY_AUTH_KEY,
      authMeta
    );
    return !!result;
  }

  async getAuthMetadata(userId?: string): Promise<DigikeyAuthMeta | null> {
    const userIdToUse = userId || this.LEGACY_USER_ID;
    const data = await this.metadataService.getUserMetaData(
      userIdToUse,
      this.DIGIKEY_AUTH_KEY
    );

    if (!data) return null;

    // Ensure proper date conversion
    const authMeta = data as unknown as DigikeyAuthMeta;

    // Convert string dates back to Date objects if needed
    if (typeof authMeta.userTokenExpires === 'string') {
      authMeta.userTokenExpires = new Date(authMeta.userTokenExpires);
    }

    if (typeof authMeta.userRefreshTokenExpires === 'string') {
      authMeta.userRefreshTokenExpires = new Date(authMeta.userRefreshTokenExpires);
    }

    return authMeta;
  }

  // Legacy methods for backward compatibility
  async saveAuthMetadataLegacy(authMeta: DigikeyAuthMeta): Promise<boolean> {
    return this.saveAuthMetadata(authMeta, this.LEGACY_USER_ID);
  }

  async getAuthMetadataLegacy(): Promise<DigikeyAuthMeta | null> {
    return this.getAuthMetadata(this.LEGACY_USER_ID);
  }
}
