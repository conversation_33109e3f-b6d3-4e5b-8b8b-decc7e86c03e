import { UseGuards } from "@nestjs/common";
import { Args, Mutation, Query, Resolver } from "@nestjs/graphql";

import { CurrentUser } from "../auth/decorators/current-user.decorator";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import type { AuthUser } from "../auth/interfaces/auth-user.interface";
import type { UpdateSupplierPreferenceInput } from "../common/types";
import { SupplierRepository } from "./supplier.repo";

@Resolver("SupplierPreference")
@UseGuards(JwtAuthGuard)
export class SupplierResolver {
  constructor(private readonly supplierRepo: SupplierRepository) {}

  @Query("supplierPreferences")
  async getSupplierPreferences(@CurrentUser() user: AuthUser) {
    return this.supplierRepo.getAllSupplierPreferences();
  }

  @Query("supplierPreferenceBySellerKey")
  async getSupplierPreferenceBySellerKey(
    @Args("sellerKey") sellerKey: string,
    @CurrentUser() user: AuthUser,
  ) {
    return this.supplierRepo.getSupplierPreferenceBySellerKey(sellerKey);
  }

  @Mutation("updateSupplierPreference")
  async updateSupplierPreference(
    @Args("input") input: UpdateSupplierPreferenceInput,
    @CurrentUser() user: AuthUser,
  ) {
    return this.supplierRepo.updateSupplierPreference(input);
  }
}
