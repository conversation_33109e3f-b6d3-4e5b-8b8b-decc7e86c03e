import { ApolloServerPluginLandingPageLocalDefault } from "@apollo/server/plugin/landingPage/default";
import { ApolloDriver, ApolloDriverConfig } from "@nestjs/apollo";
import { HttpModule, HttpService } from "@nestjs/axios";
import { Module } from "@nestjs/common";
import { GraphQLModule } from "@nestjs/graphql";

import { AuthModule } from "./auth/auth.module";
import { BomModule } from "./bom/bom.module";
import { CurrencyModule } from "./currency/currency.module";
import { MetadataModule } from "./metadata/metadata.module";
import { PackagingModule } from "./packaging/packaging.module";
import { PartModule } from "./part/part.module";
import { PrismaModule } from "./prisma/prisma.module";
import { ProductModule } from "./product/product.module";
import { RfqModule } from "./rfq/rfq.module";
import { setupHttpInterceptors } from "./shared/utils/http-interceptor.util";
import { SupplierModule } from "./supplier/supplier.module";
import { SupplyModule } from "./supply/supply.module";
import { Z2DataModule } from "./z2data/z2data.module";

@Module({
  imports: [
    GraphQLModule.forRoot<ApolloDriverConfig>({
      driver: ApolloDriver,
      introspection: true,
      playground: false,
      plugins: [ApolloServerPluginLandingPageLocalDefault()],
      typePaths: ["./**/*.graphql"],
      context: ({ req }: { req: any }) => ({
        req,
        user: req.user,
        isAuthenticated: !!req.user
      }),
    }),
    AuthModule,
    BomModule,
    RfqModule,
    PartModule,
    MetadataModule,
    PackagingModule,
    SupplierModule,
    SupplyModule,
    ProductModule,
    CurrencyModule,
    HttpModule,
    PrismaModule,
    Z2DataModule,
  ],
  providers: [
    {
      inject: [HttpService],
      provide: "APP_INITIALIZER",
      useFactory: (httpService: HttpService) => {
        setupHttpInterceptors(httpService);
      },
    },
  ],
})
export class AppModule {}
