import { NextResponse } from "next/server";
import { createZodRoute } from "next-zod-route";
import { z } from "zod";

import { handleApiError } from "@/lib/api-error-handler";
import { prisma } from "@/lib/prisma";
import { partSchema } from "@/lib/schemas";

import { Prisma } from "../../../generated/client";

const searchParamsSchema = z.object({
  id: z.coerce.number(),
});

const buildPartFilters = ({ id }: z.infer<typeof searchParamsSchema>) => {
  const baseFilter: Prisma.PartWhereInput = {
    partId: id,
  };

  return baseFilter;
};

export const GET = createZodRoute()
  .query(searchParamsSchema)
  .handler(async (request, context) => {
    try {
      const { id } = context.query;

      const where = buildPartFilters({
        id,
      });

      const part = await prisma.part.findFirstOrThrow({
        relationLoadStrategy: "query",
        select: {
          grade: {
            select: {
              name: true,
            },
          },
          manufacturer: {
            select: {
              name: true,
            },
          },
          package: {
            select: {
              name: true,
            },
          },
          partId: true,
          productLine: {
            select: {
              name: true,
            },
          },
          partNumber: true,
        },
        where,
      });

      const transformedPart = {
        id: Number(part.partId),
        name: part.partNumber,
        grade: part.grade?.name ?? null,
        manufacturer: part.manufacturer?.name ?? null,
        package: part.package?.name ?? null,
        pl: part.productLine?.name ?? null,
      };

      return NextResponse.json(partSchema.parse(transformedPart));
    } catch (error) {
      return handleApiError(error, {
        query: context.query,
        resourceId: context.query.id,
        resourceName: "part",
      });
    }
  });
