import { NextResponse } from "next/server";
import { createZodRoute } from "next-zod-route";
import { z } from "zod";

import { handleApiError } from "@/lib/api-error-handler";
import { prisma } from "@/lib/prisma";
import { type Part, partSchema } from "@/lib/schemas";

import { Prisma } from "../../../generated/client";

const searchParamsSchema = z.object({
  id: z.coerce.number().optional(),
  query: z.string().optional(),
});

const buildPartFilters = ({
  id,
  query,
}: z.infer<typeof searchParamsSchema>) => {
  const queryAsInt = query ? Number(query) : NaN;
  const isNumericQuery = !isNaN(queryAsInt);

  const baseFilter: Prisma.PartWhereInput = {
    ...(id && { partId: id }),
    ...(query && {
      OR: [
        { partNumber: { contains: query, mode: "insensitive" } },
        ...(isNumericQuery ? [{ partId: queryAsInt }] : []),
      ],
    }),
  };

  return baseFilter;
};

export const GET = createZodRoute()
  .query(searchParamsSchema)
  .handler(async (request, context) => {
    try {
      const where = buildPartFilters(context.query);

      const parts = await prisma.part.findMany({
        orderBy: { partNumber: "asc" },
        select: {
          partId: true,
          partNumber: true,
        },
        take: 20,
        where,
      });

      const transformedParts = parts
        .filter((part) => part.partNumber !== null)
        .map<Part>((part) => ({
          id: part.partId,
          name: part.partNumber!,
        }));

      return NextResponse.json(partSchema.array().parse(transformedParts));
    } catch (error) {
      return handleApiError(error, {
        query: context.query,
        resourceId: context.query.id,
        resourceName: "part",
      });
    }
  });
