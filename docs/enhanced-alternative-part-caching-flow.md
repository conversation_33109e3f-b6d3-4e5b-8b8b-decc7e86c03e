## Flow Diagram

```mermaid
flowchart TD
    A[getPartAlternatives called] --> B[Check cached alternatives]
    B --> C{Cache exists and not expired?}
    C -->|Yes| D[Return cached data with part joins]
    C -->|No| E[Call Z2Data Cross API]
    E --> F{Z2Data API successful?}
    F -->|No| G[Return null]
    F -->|Yes| H[Process Alternative Items]
    H --> I[For each alternative item]
    I --> J[Search locally by exact MPN + Manufacturer]
    J --> K{Found locally?}
    K -->|Yes| L[Map local part ID]
    K -->|No| M[Search by keyword]
    M --> N{Exact match found in keyword results?}
    N -->|Yes| O[Map found part ID]
    N -->|No| P[Call Z2Data validation API]
    P --> Q{Part created from Z2Data?}
    Q -->|Yes| R[Map new part ID]
    Q -->|No| S[Skip this alternative - part_id required]
    L --> T[Continue to next item]
    O --> T
    R --> T
    S --> T
    T --> U{More items to process?}
    U -->|Yes| I
    U -->|No| V[Cache alternatives with part references]
    V --> W[Fetch complete part data for each alternative]
    W --> X[Build response with part data]
    X --> Y[Return enhanced response]
    D --> Z[Response includes part data via joins]
    Y --> AA[Frontend receives alternatives with complete part info]
    Z --> AA
```
