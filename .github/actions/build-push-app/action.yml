name: Build & push app
description: This builds image & push to ECR repo
inputs:
  project-name:
    description: "The name of the project"
    required: true
  app-name:
    description: "The name of app to build"
    required: true
  aws-region:
    description: "The AWS region to deploy to"
    required: true
  aws-account-id:
    description: "The AWS account ID to deploy to"
    required: true
  environment:
    description: "The name of the environment where to deploy to"
    required: true
  dockerhub-username:
    description: "<PERSON>rname used to log against the DockerHub registry"
    required: true
  dockerhub-pat:
    description: "Personal access token used to log against the DockerHub registry"
    required: true
  image-tag-prefix:
    description: "Prefix for image tag"
    required: true
  app-version:
    description: "The version of the app to build"
    required: true
  api-base:
    description: "The base URL of the API"
    required: true
  mi-base-url-path:
    description: "The base URL path of the Market Intelligence API"
    required: true

runs:
  using: composite
  steps:
    - uses: ./.github/actions/prepare

    - name: Login to Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ inputs.dockerhub-username }}
        password: ${{ inputs.dockerhub-pat }}

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - uses: ./.github/actions/setup-aws
      with:
        aws-region: ${{ inputs.aws-region }}
        aws-account-id: ${{ inputs.aws-account-id }}
        environment: ${{ inputs.environment }}

    - name: Login to Amazon ECR
      uses: aws-actions/amazon-ecr-login@v2

    - name: Get the application version
      id: app-version
      uses: ./.github/actions/app-version

    - name: Build app
      shell: bash
      env:
        NEXT_PUBLIC_API_BASE: ${{ inputs.api-base }}
        NEXT_PUBLIC_MI_API_BASE_URL: ${{ inputs.api-base }}${{ inputs.mi-base-url-path }}/api
      run: |
        pnpm nx run ${{ inputs.app-name }}:build

    - uses: actions/cache@v4
      with:
        path: |
          ~/.npm
          ${{ github.workspace }}/.next/cache
        key: ${{ runner.os }}-nextjs-${{ hashFiles('**/package-lock.json') }}-${{ hashFiles('**/*.js', '**/*.jsx', '**/*.ts', '**/*.tsx') }}
        restore-keys: |
          ${{ runner.os }}-nextjs-${{ hashFiles('**/package-lock.json') }}-

    - name: Build image & push to ECR repo
      uses: docker/build-push-action@v6
      with:
        context: .
        file: apps/${{ inputs.app-name }}/Dockerfile
        build-args: |
          app_name=${{ inputs.app-name }}
          git_hash=${{ inputs.app-version }}
        cache-from: |
          type=registry,ref=${{ inputs.aws-account-id }}.dkr.ecr.${{ inputs.aws-region }}.amazonaws.com/repository-${{ inputs.project-name }}-${{ inputs.environment }}-${{ inputs.app-name }}:cache
        cache-to: |
          type=registry,mode=max,image-manifest=true,oci-mediatypes=true,ignore-error=true,ref=${{ inputs.aws-account-id }}.dkr.ecr.${{ inputs.aws-region }}.amazonaws.com/repository-${{ inputs.project-name }}-${{ inputs.environment }}-${{ inputs.app-name }}:cache
        platforms: |
          linux/amd64
        tags: |
          ${{ inputs.aws-account-id }}.dkr.ecr.${{ inputs.aws-region }}.amazonaws.com/repository-${{ inputs.project-name }}-${{ inputs.environment }}-${{ inputs.app-name }}:latest
          ${{ inputs.aws-account-id }}.dkr.ecr.${{ inputs.aws-region }}.amazonaws.com/repository-${{ inputs.project-name }}-${{ inputs.environment }}-${{ inputs.app-name }}:${{ inputs.app-version }}
        push: true
