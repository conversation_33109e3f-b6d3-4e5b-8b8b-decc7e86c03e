import { AwsProvider } from "@cdktf/provider-aws/lib/provider";
import { TerraformStack } from "cdktf";
import { Construct } from "constructs";

import {
  GitHubActionsConstruct,
  RemoteBackendConstruct,
  StateManagement,
} from "../constructs";

type BootstrapStackProps = {
  environment: string;
  awsRegion: string;
  githubOrg: string;
  githubRepo: string;
  shouldUseExistingGithubOpenIdConnect: boolean;
  remoteBackendBucketName: string;
  remoteBackendStateKeyPrefix: string;
  remoteBackendLockTableName: string;
};

export class BootstrapStack extends TerraformStack {
  constructor(scope: Construct, id: string, props: BootstrapStackProps) {
    super(scope, id);

    new AwsProvider(this, "AWS", {
      region: props.awsRegion,
    });

    new RemoteBackendConstruct(this, "s3-backend", {
      awsRegion: props.awsRegion,
      bucketName: props.remoteBackendBucketName,
      lockTableName: props.remoteBackendLockTableName,
      stackId: id,
      stateKeyPrefix: props.remoteBackendStateKeyPrefix,
    });

    new StateManagement(this, "state-management", {
      bucketName: props.remoteBackendBucketName,
      environment: props.environment,
      lockTableName: props.remoteBackendLockTableName,
      region: props.awsRegion,
      stateKeyPrefix: props.remoteBackendStateKeyPrefix,
    });

    new GitHubActionsConstruct(this, "github-actions-construct", {
      awsRegion: props.awsRegion,
      environment: props.environment,
      githubOrg: props.githubOrg,
      githubRepo: props.githubRepo,
      shouldUseExistingGithubOpenIdConnect:
        props.shouldUseExistingGithubOpenIdConnect,
    });
  }
}
