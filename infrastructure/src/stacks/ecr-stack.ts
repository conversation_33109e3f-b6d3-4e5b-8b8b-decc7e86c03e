import { AwsProvider } from "@cdktf/provider-aws/lib/provider";
import { TerraformStack } from "cdktf";
import { Construct } from "constructs";

import { RemoteBackendConstruct, Repository } from "../constructs";

type EcrStackProps = {
  environment: string;
  awsRegion: string;
  projectName: string;
  servicesToDeploy: string[];
  remoteBackendBucketName: string;
  remoteBackendStateKeyPrefix: string;
  remoteBackendLockTableName: string;
};

export class EcrStack extends TerraformStack {
  public readonly ecrRepositories: Record<string, Repository>;

  constructor(scope: Construct, id: string, props: EcrStackProps) {
    super(scope, id);

    new AwsProvider(this, "AWS", {
      region: props.awsRegion,
    });

    new RemoteBackendConstruct(this, "s3-backend", {
      awsRegion: props.awsRegion,
      bucketName: props.remoteBackendBucketName,
      lockTableName: props.remoteBackendLockTableName,
      stackId: id,
      stateKeyPrefix: props.remoteBackendStateKeyPrefix,
    });

    this.ecrRepositories = Object.fromEntries(
      props.servicesToDeploy.map((service) => {
        const repositoryName =
          `repository-${props.projectName}-${props.environment}-${service}`.toLowerCase();
        return [
          service,
          new Repository(this, repositoryName, {
            name: repositoryName,
            forceDelete: false,
          }),
        ];
      }),
    );
  }

  public getRepository(serviceName: string): Repository {
    const repo = this.ecrRepositories[serviceName];
    if (!repo) {
      throw new Error(`Repository for service ${serviceName} not found`);
    }
    return repo;
  }
}
