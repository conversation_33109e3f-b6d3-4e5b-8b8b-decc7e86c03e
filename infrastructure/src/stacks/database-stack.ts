import { DbSubnetGroup } from "@cdktf/provider-aws/lib/db-subnet-group";
import { AwsProvider } from "@cdktf/provider-aws/lib/provider";
import { RandomProvider } from "@cdktf/provider-random/lib/provider";
import { TerraformOutput, TerraformStack } from "cdktf";
import { Construct } from "constructs";

import { RemoteBackendConstruct } from "../constructs";
import { RdsInstance } from "../constructs/rds-instance";

type DatabaseStackProps = {
  privateSubnetIds: string[];
  vpcId: string;
  awsRegion: string;
  kmsKeyId: string;
  projectName: string;
  environment: string;
  databaseSecurityGroupId: string;
  remoteBackendBucketName: string;
  remoteBackendStateKeyPrefix: string;
  remoteBackendLockTableName: string;
  tags: Record<string, string>;
};

export class DatabaseStack extends TerraformStack {
  public readonly dbInstanceEndpoint: string;
  public readonly dbInstanceIdentifier: string;
  public readonly dbSecretArn: string;

  constructor(scope: Construct, id: string, props: DatabaseStackProps) {
    super(scope, id);

    new AwsProvider(this, "AWS", {
      region: props.awsRegion,
    });

    new RandomProvider(this, "Random");

    new RemoteBackendConstruct(this, "s3-backend", {
      awsRegion: props.awsRegion,
      bucketName: props.remoteBackendBucketName,
      lockTableName: props.remoteBackendLockTableName,
      stackId: id,
      stateKeyPrefix: props.remoteBackendStateKeyPrefix,
    });

    const subnetGroup = new DbSubnetGroup(this, "db-subnet-group", {
      name: `${props.projectName}-${props.environment.toLowerCase()}-db-subnet-group`,
      subnetIds: props.privateSubnetIds,
      tags: {
        ...props.tags,
        Name: `${props.projectName}-${props.environment.toLowerCase()}-db-subnet-group`,
      },
    });

    const dbInstance = new RdsInstance(this, "rds-instance", {
      allocatedStorage: 20,
      dbName: "swiftdb",
      dbSubnetGroupName: subnetGroup.name,
      engine: "postgres",
      engineVersion: "17.4",
      instanceClass: "db.t3.micro",
      kmsKeyId: props.kmsKeyId,
      name: `${props.projectName}-${props.environment.toLowerCase()}-postgres`,
      parameterGroupName: "default.postgres17",
      securityGroupId: props.databaseSecurityGroupId,
      storageType: "gp2",
      subnetIds: props.privateSubnetIds,
      tags: {
        ...props.tags,
        Name: `${props.projectName}-${props.environment.toLowerCase()}-postgres`,
      },
      username: "pgadmin",
      vpcId: props.vpcId,
    });

    this.dbInstanceEndpoint = dbInstance.endpoint;

    new TerraformOutput(this, "DbInstanceEndpoint", {
      description: "The connection endpoint for the RDS instance",
      value: dbInstance.endpoint,
    });

    this.dbInstanceIdentifier = dbInstance.identifier;

    this.dbSecretArn = dbInstance.masterUserSecretArn;
  }
}
