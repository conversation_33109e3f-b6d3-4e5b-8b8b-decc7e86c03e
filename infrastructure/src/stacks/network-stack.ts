import { LbTargetGroup } from "@cdktf/provider-aws/lib/lb-target-group";
import { AwsProvider } from "@cdktf/provider-aws/lib/provider";
import { TerraformOutput, TerraformStack } from "cdktf";
import { Construct } from "constructs";

import { LoadBalancer } from "../constructs/load-balancer";
import { RemoteBackendConstruct } from "../constructs/remote-backend";
import { SharedVpc } from "../constructs/vpc";

export interface ServiceRouteConfig {
  name: string;
  pathPatterns: string[];
  port: number;
  priority: number;
  healthCheckPath?: string;
}

type NetworkStackProps = {
  awsRegion: string;
  environment: string;
  projectName: string;
  defaultService: string;
  serviceRoutes: ServiceRouteConfig[];
  gwaihirVpcId: string;
  gwaihirPeerConnectionCidrBlock: string;
  remoteBackendBucketName: string;
  remoteBackendStateKeyPrefix: string;
  remoteBackendLockTableName: string;
};

export class NetworkStack extends TerraformStack {
  public readonly loadBalancer: LoadBalancer;
  public readonly targetGroups: Record<string, LbTargetGroup> = {};

  public readonly vpcId: string;
  public readonly publicSubnetIds: string[];
  public readonly privateSubnetIds: string[];

  constructor(scope: Construct, id: string, props: NetworkStackProps) {
    super(scope, id);

    new AwsProvider(this, "AWS", {
      region: props.awsRegion,
    });

    new RemoteBackendConstruct(this, "s3-backend", {
      awsRegion: props.awsRegion,
      bucketName: props.remoteBackendBucketName,
      lockTableName: props.remoteBackendLockTableName,
      stackId: id,
      stateKeyPrefix: props.remoteBackendStateKeyPrefix,
    });
    const namePrefix = `${props.projectName}-${props.environment}`;

    const vpc = new SharedVpc(this, "shared-vpc", {
      awsRegion: props.awsRegion,
      cidrBlock: "10.0.0.0/16",
      gwaihirPeerConnectionCidrBlock: props.gwaihirPeerConnectionCidrBlock,
      gwaihirVpcId: props.gwaihirVpcId,
      namePrefix,
    });
    this.vpcId = vpc.vpcId;
    this.publicSubnetIds = vpc.publicSubnetIds;
    this.privateSubnetIds = vpc.privateSubnetIds;
    new TerraformOutput(this, "private-subnet-ids", {
      description: "The IDs of the private subnets",
      value: vpc.privateSubnetIds,
    });

    props.serviceRoutes.forEach((serviceRoute) => {
      const healthCheckPath = serviceRoute.healthCheckPath || "/health";

      const tgName = this._generateTargetGroupName(
        namePrefix,
        serviceRoute.name,
      );

      this.targetGroups[serviceRoute.name] = new LbTargetGroup(
        this,
        `tg-${serviceRoute.name}`,
        {
          healthCheck: {
            healthyThreshold: 3,
            interval: 30,
            matcher: "200-399",
            path: healthCheckPath,
            port: "traffic-port",
            protocol: "HTTP",
            timeout: 5,
            unhealthyThreshold: 3,
          },
          name: tgName,
          port: serviceRoute.port,
          protocol: "HTTP",
          tags: {
            Name: `${namePrefix}-${serviceRoute.name}-tg`,
          },
          targetType: "ip",
          vpcId: vpc.vpcId,
        },
      );
    });

    if (!this.targetGroups[props.defaultService]) {
      throw new Error(
        `Default service ${props.defaultService} not found in service routes`,
      );
    }

    this.loadBalancer = new LoadBalancer(this, "shared-alb", {
      defaultTargetGroupArn: this.targetGroups[props.defaultService].arn,
      namePrefix,
      port: 80,
      protocol: "HTTP",
      publicSubnets: vpc.publicSubnetIds,
      vpcId: vpc.vpcId,
    });

    const pathBasedRoutes = props.serviceRoutes
      .filter((route) => route.name !== props.defaultService)
      .map((route) => ({
        pathPatterns: route.pathPatterns,
        priority: route.priority,
        targetGroupArn: this.targetGroups[route.name].arn,
      }));

    if (pathBasedRoutes.length > 0) {
      this.loadBalancer.addPathBasedRoutes(pathBasedRoutes);
    }

    new TerraformOutput(this, "load-balancer-dns", {
      description: "The DNS name of the load balancer",
      value: this.loadBalancer.loadBalancer.dnsName,
    });

    props.serviceRoutes.forEach((serviceRoute) => {
      new TerraformOutput(this, `tg-${serviceRoute.name}-arn`, {
        description: `Target group ARN for ${serviceRoute.name}`,
        value: this.targetGroups[serviceRoute.name].arn,
      });
    });
  }

  public getTargetGroup(serviceName: string): LbTargetGroup {
    const targetGroup = this.targetGroups[serviceName];
    if (!targetGroup) {
      throw new Error(`Target group for service ${serviceName} not found`);
    }
    return targetGroup;
  }

  private _generateTargetGroupName(
    namePrefix: string,
    serviceName: string,
  ): string {
    const suffix = "-tg";
    const fixedLength = namePrefix.length + 1 + suffix.length;
    const maxLength = 32;
    const availableLength = maxLength - fixedLength;
    const serviceNamePart = serviceName.substring(0, availableLength);
    return `${namePrefix}-${serviceNamePart}${suffix}`;
  }
}
