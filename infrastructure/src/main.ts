import { App } from "cdktf";

import { getConfig } from "./config/get-config";
import { AuthStack } from "./stacks/auth-stack";
import { BootstrapStack } from "./stacks/bootstrap-stack";
import { CloudFrontStack } from "./stacks/cloudfront-stack";
import { DatabaseStack } from "./stacks/database-stack";
import { EcrStack } from "./stacks/ecr-stack";
import { EcsStack } from "./stacks/ecs-stack";
import { KmsStack } from "./stacks/kms-stack";
import { NetworkStack, ServiceRouteConfig } from "./stacks/network-stack";

const app = new App();

const { config, environment } = getConfig();

type ServiceConfigForEcs = {
  accessToDatabase: boolean;
  containerPort: number;
  desiredCount: number;
  cpu: string;
  memory: string;
  containerEnvironment?: Record<string, string | undefined>;
};

type ServiceConfigForAuth = {
  containerPort: number;
  accessToDatabase: boolean;
};

const serviceConfigs: Record<string, ServiceConfigForEcs> = {};
const serviceConfigsForAuth: Record<string, ServiceConfigForAuth> = {};
const serviceRoutes: ServiceRouteConfig[] = [];
const serviceNames = Object.keys(
  config.ecsConfig,
) as (keyof typeof config.ecsConfig)[];

serviceNames.forEach((serviceName) => {
  const serviceConfig = config.ecsConfig[serviceName];
  const containerPort = serviceConfig.containerPort;

  serviceConfigs[serviceName] = {
    accessToDatabase: serviceConfig.accessToDatabase,
    containerEnvironment: serviceConfig.containerEnvironment,
    containerPort,
    cpu: serviceConfig.cpu || "256",
    desiredCount: serviceConfig.desiredCount || 1,
    memory: serviceConfig.memory || "512",
  };

  serviceConfigsForAuth[serviceName] = {
    accessToDatabase: serviceConfig.accessToDatabase || false,
    containerPort,
  };

  serviceRoutes.push({
    healthCheckPath: serviceConfig.healthCheckPath,
    name: serviceName,
    pathPatterns: serviceConfig.pathPatterns || ["/*"],
    port: containerPort,
    priority: serviceConfig.priority || 1,
  });
});

serviceRoutes.sort((a, b) => b.priority - a.priority);

const bootstrapStack = new BootstrapStack(app, "bootstrap-stack", {
  awsRegion: config.awsRegion,
  environment,
  githubOrg: config.githubOrg,
  githubRepo: config.githubRepo,
  remoteBackendBucketName: config.remoteBackend.bucketName,
  remoteBackendLockTableName: config.remoteBackend.lockTableName,
  remoteBackendStateKeyPrefix: config.remoteBackend.stateKeyPrefix,
  shouldUseExistingGithubOpenIdConnect:
    config.shouldUseExistingGithubOpenIdConnect,
});

const ecrStack = new EcrStack(app, "ecr-stack", {
  awsRegion: config.awsRegion,
  environment,
  remoteBackendBucketName: config.remoteBackend.bucketName,
  remoteBackendLockTableName: config.remoteBackend.lockTableName,
  remoteBackendStateKeyPrefix: config.remoteBackend.stateKeyPrefix,
  servicesToDeploy: serviceNames,
  projectName: config.githubRepo,
});

ecrStack.addDependency(bootstrapStack);

const kmsStack = new KmsStack(app, "kms-stack", {
  awsRegion: config.awsRegion,
  environment,
  projectName: config.githubRepo,
  remoteBackendBucketName: config.remoteBackend.bucketName,
  remoteBackendLockTableName: config.remoteBackend.lockTableName,
  remoteBackendStateKeyPrefix: config.remoteBackend.stateKeyPrefix,
  tags: config.tags,
});
kmsStack.addDependency(bootstrapStack);

const networkStack = new NetworkStack(app, "network-stack", {
  awsRegion: config.awsRegion,
  defaultService: config.defaultService,
  environment,
  gwaihirVpcId: config.gwaihirVpcId,
  gwaihirPeerConnectionCidrBlock: config.gwaihirPeerConnectionCidrBlock,
  projectName: config.githubRepo,
  remoteBackendBucketName: config.remoteBackend.bucketName,
  remoteBackendLockTableName: config.remoteBackend.lockTableName,
  remoteBackendStateKeyPrefix: config.remoteBackend.stateKeyPrefix,
  serviceRoutes,
});
networkStack.addDependency(bootstrapStack);

const authStack = new AuthStack(app, "auth-stack", {
  awsRegion: config.awsRegion,
  environment,
  loadBalancerSecurityGroupId: networkStack.loadBalancer.securityGroup.id,
  projectName: config.githubRepo,
  remoteBackendBucketName: config.remoteBackend.bucketName,
  remoteBackendLockTableName: config.remoteBackend.lockTableName,
  remoteBackendStateKeyPrefix: config.remoteBackend.stateKeyPrefix,
  serviceConfigs: serviceConfigsForAuth,
  servicesToDeploy: serviceNames,
  tags: config.tags,
  vpcId: networkStack.vpcId,
});
networkStack.addDependency(bootstrapStack);
authStack.addDependency(networkStack);

const databaseStack = new DatabaseStack(app, "database-stack", {
  awsRegion: config.awsRegion,
  databaseSecurityGroupId: authStack.securityGroups.database.id,
  environment,
  kmsKeyId: kmsStack.kmsKeyId,
  privateSubnetIds: networkStack.privateSubnetIds,
  projectName: config.githubRepo,
  remoteBackendBucketName: config.remoteBackend.bucketName,
  remoteBackendLockTableName: config.remoteBackend.lockTableName,
  remoteBackendStateKeyPrefix: config.remoteBackend.stateKeyPrefix,
  tags: config.tags,
  vpcId: networkStack.vpcId,
});
databaseStack.addDependency(bootstrapStack);
databaseStack.addDependency(networkStack);
databaseStack.addDependency(kmsStack);
databaseStack.addDependency(authStack);

const ecsStack = new EcsStack(app, "ecs-stack", {
  awsRegion: config.awsRegion,
  dbKmsKeyArn: kmsStack.kmsKeyArn,
  dbSecretArn: databaseStack.dbSecretArn,
  ecrRepositories: ecrStack.ecrRepositories,
  environment,
  imageTag: config.imageTag,
  privateSubnetIds: networkStack.privateSubnetIds,
  projectName: config.githubRepo,
  remoteBackendBucketName: config.remoteBackend.bucketName,
  remoteBackendLockTableName: config.remoteBackend.lockTableName,
  remoteBackendStateKeyPrefix: config.remoteBackend.stateKeyPrefix,
  serviceConfigs,
  serviceSecurityGroups: authStack.securityGroups,
  servicesToDeploy: serviceNames,
  tags: config.tags,
  targetGroups: networkStack.targetGroups,
  vpcId: networkStack.vpcId,
});
ecsStack.addDependency(bootstrapStack);
ecsStack.addDependency(ecrStack);
ecsStack.addDependency(networkStack);
ecsStack.addDependency(kmsStack);
ecsStack.addDependency(databaseStack);
ecsStack.addDependency(authStack);

const cloudFrontStack = new CloudFrontStack(app, "cloudfront-stack", {
  awsRegion: config.awsRegion,
  environment,
  projectName: config.githubRepo,
  albDnsName: networkStack.loadBalancer.loadBalancer.dnsName,
  customDomainNames: config.customDomainNames,
  certificateArn: config.certificateArn,
  remoteBackendBucketName: config.remoteBackend.bucketName,
  remoteBackendLockTableName: config.remoteBackend.lockTableName,
  remoteBackendStateKeyPrefix: config.remoteBackend.stateKeyPrefix,
  tags: config.tags,
});
cloudFrontStack.addDependency(bootstrapStack);
cloudFrontStack.addDependency(networkStack);

app.synth();
