import { DbInstance } from "@cdktf/provider-aws/lib/db-instance";
import { SecretsmanagerSecret } from "@cdktf/provider-aws/lib/secretsmanager-secret";
import { SecretsmanagerSecretVersion } from "@cdktf/provider-aws/lib/secretsmanager-secret-version";
import { password } from "@cdktf/provider-random";
import { Construct } from "constructs";

export type RdsInstanceProps = {
  name: string;
  engine: string;
  engineVersion: string;
  instanceClass: string;
  dbName: string;
  username: string;
  allocatedStorage: number;
  storageType: string;
  subnetIds: string[];
  vpcId: string;
  kmsKeyId: string;
  dbSubnetGroupName: string;
  securityGroupId: string;
  parameterGroupName: string;
  tags: Record<string, string>;
};

export class RdsInstance extends Construct {
  public readonly endpoint: string;
  public readonly identifier: string;
  public readonly masterUserSecretArn: string;

  constructor(scope: Construct, id: string, props: RdsInstanceProps) {
    super(scope, id);

    const dbPassword = new password.Password(this, "db-password", {
      length: 32,
      special: false,
    });

    const rdsInstance = new DbInstance(this, "rds-instance", {
      allocatedStorage: props.allocatedStorage,
      backupRetentionPeriod: 7,
      backupWindow: "03:00-04:00",
      dbName: props.dbName,
      dbSubnetGroupName: props.dbSubnetGroupName,
      engine: props.engine,
      engineVersion: props.engineVersion,
      identifier: props.name,
      instanceClass: props.instanceClass,
      parameterGroupName: props.parameterGroupName,
      password: dbPassword.result,
      skipFinalSnapshot: true,
      storageEncrypted: true,
      storageType: props.storageType,
      tags: {
        ...props.tags,
        Name: props.name,
      },
      username: props.username,
      vpcSecurityGroupIds: [props.securityGroupId],
      lifecycle: {
        ignoreChanges: ["password"],
      },
    });

    this.endpoint = rdsInstance.endpoint;

    const endpointSecret = new SecretsmanagerSecret(this, "endpoint-secret", {
      kmsKeyId: props.kmsKeyId,
      name: `${props.name}-connection`,
      tags: {
        ...props.tags,
        Name: `${props.name}-connection`,
      },
    });

    new SecretsmanagerSecretVersion(this, "endpoint-secret-version", {
      dependsOn: [rdsInstance],
      secretId: endpointSecret.id,
      secretString: JSON.stringify({
        DATABASE_ENGINE: props.engine,
        DATABASE_HOST: rdsInstance.endpoint,
        DATABASE_NAME: props.dbName,
        DATABASE_PASSWORD: dbPassword.result,
        DATABASE_PORT: "5432",
        DATABASE_URL: `postgresql://${props.username}:${dbPassword.result}@${rdsInstance.endpoint}/${props.dbName}`,
        DATABASE_USERNAME: props.username,
      }),
      lifecycle: {
        ignoreChanges: ["secret_string"],
      },
    });

    this.masterUserSecretArn = endpointSecret.arn;
    this.identifier = rdsInstance.identifier;
  }
}
