import { DynamodbTable } from "@cdktf/provider-aws/lib/dynamodb-table";
import { S3Bucket } from "@cdktf/provider-aws/lib/s3-bucket";
import { S3BucketPublicAccessBlock } from "@cdktf/provider-aws/lib/s3-bucket-public-access-block";
import { S3BucketServerSideEncryptionConfigurationA } from "@cdktf/provider-aws/lib/s3-bucket-server-side-encryption-configuration";
import { S3BucketVersioningA } from "@cdktf/provider-aws/lib/s3-bucket-versioning";
import { Construct } from "constructs";

export interface StateManagementProps {
  bucketName: string;
  lockTableName: string;
  stateKeyPrefix: string;
  environment: string;
  region: string;
}

export class StateManagement extends Construct {
  constructor(scope: Construct, id: string, props: StateManagementProps) {
    super(scope, id);

    const stateBucket = new S3Bucket(this, "terraform-state-bucket", {
      bucket: props.bucketName,
    });

    new S3BucketVersioningA(this, "state-bucket-versioning", {
      bucket: stateBucket.id,
      versioningConfiguration: {
        status: "Enabled",
      },
    });

    new S3BucketServerSideEncryptionConfigurationA(
      this,
      "state-bucket-encryption",
      {
        bucket: stateBucket.id,
        rule: [
          {
            applyServerSideEncryptionByDefault: {
              sseAlgorithm: "AES256",
            },
          },
        ],
      },
    );

    new S3BucketPublicAccessBlock(this, "state-bucket-public-access-block", {
      blockPublicAcls: true,
      blockPublicPolicy: true,
      bucket: stateBucket.id,
      ignorePublicAcls: true,
      restrictPublicBuckets: true,
    });

    new DynamodbTable(this, "terraform-lock-table", {
      attribute: [
        {
          name: "LockID",
          type: "S",
        },
      ],
      billingMode: "PAY_PER_REQUEST",
      hashKey: "LockID",
      name: props.lockTableName,
    });
  }
}
