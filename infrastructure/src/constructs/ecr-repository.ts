import { EcrRepository } from "@cdktf/provider-aws/lib/ecr-repository";
import { Construct } from "constructs";

type EcrRepositoryProps = {
  name: string;
  scanOnPush?: boolean;
  imageTagMutability?: "MUTABLE" | "IMMUTABLE";
  forceDelete?: boolean;
};

export class Repository extends Construct {
  public readonly repository: EcrRepository;

  constructor(scope: Construct, id: string, props: EcrRepositoryProps) {
    super(scope, id);

    this.repository = new EcrRepository(this, "Repository", {
      forceDelete: props.forceDelete ?? false,
      imageScanningConfiguration: {
        scanOnPush: props.scanOnPush ?? true,
      },
      imageTagMutability: props.imageTagMutability ?? "MUTABLE",
      name: props.name,
    });
  }
}
