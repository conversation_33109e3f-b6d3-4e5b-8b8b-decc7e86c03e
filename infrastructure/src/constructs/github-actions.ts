import { DataAwsIamOpenidConnectProvider } from "@cdktf/provider-aws/lib/data-aws-iam-openid-connect-provider";
import { IamOpenidConnectProvider } from "@cdktf/provider-aws/lib/iam-openid-connect-provider";
import { IamRole } from "@cdktf/provider-aws/lib/iam-role";
import { Construct } from "constructs";

export type GitHubActionsConstructProps = {
  awsRegion: string;
  environment: string;
  githubOrg: string;
  githubRepo: string;
  shouldUseExistingGithubOpenIdConnect: boolean;
};

export class GitHubActionsConstruct extends Construct {
  constructor(
    scope: Construct,
    id: string,
    props: GitHubActionsConstructProps,
  ) {
    super(scope, id);

    const oidcProvider =
      props.shouldUseExistingGithubOpenIdConnect === true
        ? new DataAwsIamOpenidConnectProvider(
            this,
            "existing-github-oidc-provider",
            {
              url: "https://token.actions.githubusercontent.com",
            },
          )
        : new IamOpenidConnectProvider(this, "github-oidc-provider", {
            clientIdList: ["sts.amazonaws.com"],
            thumbprintList: ["d89e3bd43d5d909b47a18977aa9d5ce36cee184c"],
            url: "https://token.actions.githubusercontent.com",
          });

    new IamRole(this, "github-actions-deploy-role", {
      assumeRolePolicy: JSON.stringify({
        Statement: [
          {
            Action: "sts:AssumeRoleWithWebIdentity",
            Condition: {
              StringEquals: {
                "token.actions.githubusercontent.com:aud": "sts.amazonaws.com",
              },
              StringLike: {
                "token.actions.githubusercontent.com:sub": `repo:${props.githubOrg}/${props.githubRepo}:environment:${props.environment}`,
              },
            },
            Effect: "Allow",
            Principal: {
              Federated: oidcProvider.arn,
            },
          },
        ],
        Version: "2012-10-17",
      }),

      inlinePolicy: [
        {
          name: "iam-openid-permission",
          policy: JSON.stringify({
            Statement: [
              {
                Action: [
                  "iam:GetOpenIDConnectProvider",
                  "iam:UpdateOpenIDConnectProviderThumbprint",
                ],
                Effect: "Allow",
                Resource: "*",
              },
            ],
            Version: "2012-10-17",
          }),
        },
        {
          name: "ecr-write-access",
          policy: JSON.stringify({
            Statement: [
              {
                Action: [
                  "ecr:GetAuthorizationToken",
                  "ecr:CreateRepository",
                  "ecr:GetDownloadUrlForLayer",
                  "ecr:BatchGetImage",
                  "ecr:BatchCheckLayerAvailability",
                  "ecr:PutImage",
                  "ecr:InitiateLayerUpload",
                  "ecr:UploadLayerPart",
                  "ecr:CompleteLayerUpload",
                  "ecr:TagResource",
                  "ecr:DescribeImages",
                ],
                Effect: "Allow",
                Resource: "*",
              },
            ],
            Version: "2012-10-17",
          }),
        },
        {
          name: "iam-permission",
          policy: JSON.stringify({
            Statement: [
              {
                Action: [
                  "iam:AttachRolePolicy",
                  "iam:CreateRole",
                  "iam:CreatePolicy*",
                  "iam:DeletePolicy*",
                  "iam:DeleteRole*",
                  "iam:DetachRolePolicy",
                  "iam:PassRole",
                  "iam:PutRolePolicy",
                  "iam:TagPolicy",
                  "iam:TagRole",
                  "iam:UntagPolicy",
                  "iam:UntagRole",
                  "iam:UpdateAssumeRolePolicy",
                  "iam:CreateInstanceProfile",
                  "iam:AddRoleToInstanceProfile",
                  "iam:DeleteInstanceProfile",
                  "iam:RemoveRoleFromInstanceProfile",
                ],
                Effect: "Allow",
                Resource: "*",
              },
            ],
            Version: "2012-10-17",
          }),
        },
      ],

      managedPolicyArns: [
        "arn:aws:iam::aws:policy/PowerUserAccess",
        "arn:aws:iam::aws:policy/IAMReadOnlyAccess",
      ],
      name: `github-actions-deploy-role-${props.environment}`,
    });
  }
}
