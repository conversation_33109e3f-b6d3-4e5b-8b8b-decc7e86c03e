import { Eip } from "@cdktf/provider-aws/lib/eip";
import { InternetGateway } from "@cdktf/provider-aws/lib/internet-gateway";
import { NatGateway } from "@cdktf/provider-aws/lib/nat-gateway";
import { Route } from "@cdktf/provider-aws/lib/route";
import { RouteTable } from "@cdktf/provider-aws/lib/route-table";
import { RouteTableAssociation } from "@cdktf/provider-aws/lib/route-table-association";
import { Subnet } from "@cdktf/provider-aws/lib/subnet";
import { Vpc } from "@cdktf/provider-aws/lib/vpc";
import { VpcPeeringConnection } from "@cdktf/provider-aws/lib/vpc-peering-connection";
import { Construct } from "constructs";

interface SharedVpcProps {
  namePrefix: string;
  cidrBlock: string;
  awsRegion: string;
  gwaihirVpcId: string;
  gwaihirPeerConnectionCidrBlock: string;
}

export class SharedVpc extends Construct {
  public readonly vpc: Vpc;
  public readonly publicSubnetIds: string[];
  public readonly privateSubnetIds: string[];
  public readonly vpcId: string;

  constructor(scope: Construct, id: string, props: SharedVpcProps) {
    super(scope, id);

    // Create VPC
    this.vpc = new Vpc(this, "vpc", {
      cidrBlock: props.cidrBlock,
      enableDnsHostnames: true,
      enableDnsSupport: true,
      tags: {
        Name: `${props.namePrefix}-vpc`,
      },
    });
    this.vpcId = this.vpc.id;

    // Create an Internet Gateway
    const internetGateway = new InternetGateway(this, "internet-gateway", {
      tags: {
        Name: `${props.namePrefix}-igw`,
      },
      vpcId: this.vpc.id,
    });

    // Create public subnets in different availability zones
    const publicSubnet1 = new Subnet(this, "public-subnet-1", {
      availabilityZone: `${props.awsRegion}a`,
      cidrBlock: "********/24",
      mapPublicIpOnLaunch: true,
      tags: {
        Name: `${props.namePrefix}-public-subnet-1`,
      },
      vpcId: this.vpc.id,
    });

    const publicSubnet2 = new Subnet(this, "public-subnet-2", {
      availabilityZone: `${props.awsRegion}b`,
      cidrBlock: "********/24",
      mapPublicIpOnLaunch: true,
      tags: {
        Name: `${props.namePrefix}-public-subnet-2`,
      },
      vpcId: this.vpc.id,
    });

    // Create a route table for the public subnets
    const publicRouteTable = new RouteTable(this, "public-route-table", {
      tags: {
        Name: `${props.namePrefix}-public-rt`,
      },
      vpcId: this.vpc.id,
    });

    // Create a route to the Internet Gateway
    new Route(this, "public-route", {
      destinationCidrBlock: "0.0.0.0/0",
      gatewayId: internetGateway.id,
      routeTableId: publicRouteTable.id,
    });

    // Associate the route table with the subnets
    new RouteTableAssociation(this, "rta-subnet1", {
      routeTableId: publicRouteTable.id,
      subnetId: publicSubnet1.id,
    });

    new RouteTableAssociation(this, "rta-subnet2", {
      routeTableId: publicRouteTable.id,
      subnetId: publicSubnet2.id,
    });

    this.publicSubnetIds = [publicSubnet1.id, publicSubnet2.id];

    // Create Elastic IPs for NAT Gateways
    const eip1 = new Eip(this, "eip-1", {
      tags: {
        Name: `${props.namePrefix}-eip-1`,
      },
      vpc: true,
    });

    const eip2 = new Eip(this, "eip-2", {
      tags: {
        Name: `${props.namePrefix}-eip-2`,
      },
      vpc: true,
    });

    // Create NAT Gateways
    const natGateway1 = new NatGateway(this, "nat-gateway-1", {
      allocationId: eip1.id,
      subnetId: publicSubnet1.id,
      tags: {
        Name: `${props.namePrefix}-nat-1`,
      },
    });

    const natGateway2 = new NatGateway(this, "nat-gateway-2", {
      allocationId: eip2.id,
      subnetId: publicSubnet2.id,
      tags: {
        Name: `${props.namePrefix}-nat-2`,
      },
    });

    // Create private subnets
    const privateSubnet1 = new Subnet(this, "private-subnet-1", {
      availabilityZone: `${props.awsRegion}a`,
      cidrBlock: "********/24",
      tags: {
        Name: `${props.namePrefix}-private-subnet-1`,
      },
      vpcId: this.vpc.id,
    });

    const privateSubnet2 = new Subnet(this, "private-subnet-2", {
      availabilityZone: `${props.awsRegion}b`,
      cidrBlock: "********/24",
      tags: {
        Name: `${props.namePrefix}-private-subnet-2`,
      },
      vpcId: this.vpc.id,
    });

    // Create private route tables
    const privateRouteTable1 = new RouteTable(this, "private-route-table-1", {
      tags: {
        Name: `${props.namePrefix}-private-rt-1`,
      },
      vpcId: this.vpc.id,
    });

    const privateRouteTable2 = new RouteTable(this, "private-route-table-2", {
      tags: {
        Name: `${props.namePrefix}-private-rt-2`,
      },
      vpcId: this.vpc.id,
    });

    // Create routes to NAT Gateways
    new Route(this, "private-nat-route-1", {
      destinationCidrBlock: "0.0.0.0/0",
      natGatewayId: natGateway1.id,
      routeTableId: privateRouteTable1.id,
    });

    new Route(this, "private-nat-route-2", {
      destinationCidrBlock: "0.0.0.0/0",
      natGatewayId: natGateway2.id,
      routeTableId: privateRouteTable2.id,
    });

    const peeringConnection = new VpcPeeringConnection(
      this,
      "private-peering-connection",
      {
        autoAccept: true,
        peerVpcId: props.gwaihirVpcId,
        tags: {
          Name: `${props.namePrefix}-peering`,
        },
        vpcId: this.vpc.id,
        accepter: {
          allowRemoteVpcDnsResolution: true,
        },
        requester: {
          allowRemoteVpcDnsResolution: true,
        },
      },
    );

    // --- Add routes for VPC Peering Connection ---

    new Route(this, "private-peering-route-1", {
      destinationCidrBlock: props.gwaihirPeerConnectionCidrBlock,
      routeTableId: privateRouteTable1.id,
      vpcPeeringConnectionId: peeringConnection.id,
    });

    new Route(this, "private-peering-route-2", {
      destinationCidrBlock: props.gwaihirPeerConnectionCidrBlock,
      routeTableId: privateRouteTable2.id,
      vpcPeeringConnectionId: peeringConnection.id,
    });

    // Associate private route tables with private subnets
    new RouteTableAssociation(this, "private-rta-1", {
      routeTableId: privateRouteTable1.id,
      subnetId: privateSubnet1.id,
    });

    new RouteTableAssociation(this, "private-rta-2", {
      routeTableId: privateRouteTable2.id,
      subnetId: privateSubnet2.id,
    });

    this.privateSubnetIds = [privateSubnet1.id, privateSubnet2.id];
  }
}
