export type InfrastructureConfig<TServiceName extends string> = {
  awsRegion: string;
  awsAccountId: string;

  defaultService: TServiceName;

  githubOrg: string;
  githubRepo: string;

  imageTag: string;

  customDomainNames?: string[];
  certificateArn?: string;

  gwaihirVpcId: string;
  gwaihirPeerConnectionCidrBlock: string;

  shouldUseExistingGithubOpenIdConnect: boolean;
  remoteBackend: {
    bucketName: string;
    lockTableName: string;
    stateKeyPrefix: string;
  };

  tags: Record<string, string>;

  ecsConfig?: {
    [serviceName in TServiceName]: {
      containerPort?: number;
      desiredCount?: number;
      cpu?: string;
      memory?: string;
      containerEnvironment?: Record<string, string | undefined>;
      pathPatterns?: string[];
      priority?: number;
      healthCheckPath?: string;
      accessToDatabase?: boolean;
    };
  };
};
