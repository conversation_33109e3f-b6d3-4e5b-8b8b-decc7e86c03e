export type InfrastructureConfig<TServiceName extends string> = {
  awsRegion: string;
  awsAccountId: string;

  defaultService: TServiceName;

  githubOrg: string;
  githubRepo: string;

  imageTag: string;

  customDomainNames?: string[];
  certificateArn?: string;

  gwaihirVpcId: string;
  gwaihirPeerConnectionCidrBlock: CidrBlock;

  shouldUseExistingGithubOpenIdConnect: boolean;
  remoteBackend: {
    bucketName: string;
    lockTableName: string;
    stateKeyPrefix: string;
  };

  tags: Record<string, string>;

  networkConfig: {
    cidrBlock: CidrBlock;
    publicSubnetCidrBlocks: [CidrBlock, CidrBlock];
    privateSubnetCidrBlocks: [CidrBlock, CidrBlock];
  };

  ecsConfig?: {
    [serviceName in TServiceName]: {
      containerPort?: number;
      desiredCount?: number;
      cpu?: string;
      memory?: string;
      containerEnvironment?: Record<string, string | undefined>;
      pathPatterns?: string[];
      priority?: number;
      healthCheckPath?: string;
      accessToDatabase?: boolean;
    };
  };
};
