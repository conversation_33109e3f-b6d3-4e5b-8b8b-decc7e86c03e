import { InfrastructureConfig } from "./types";

if (!process.env.AWS_ACCOUNT_ID) {
  throw new Error("AWS_ACCOUNT_ID is not set");
}

export const environments = {
  production: {
    awsAccountId: process.env.AWS_ACCOUNT_ID,
    awsRegion: "us-east-1",
    defaultService: "panel",
    customDomainNames: ["rfq-plus.int.ztwo.app"],
    certificateArn:
      "arn:aws:acm:us-east-1:************:certificate/c4ac6fef-1166-4ea5-ac2e-7f53dafdd7f1",
    ecsConfig: {
      "api-service": {
        accessToDatabase: true,
        containerEnvironment: {
          DIGIKEY_BASE_URL: "https://api.digikey.com",
          DIGIKEY_CLIENT_ID: "XAmPwbHwaWeA59xp6WbyKt5yid28ALFf",
          DIGIKEY_CLIENT_SECRET: "O3aUhnYZVUEtGVDA",
          DIGIKEY_DEFAULT_RETURN_URL: process.env.API_BASE_URL,
          DIGIKEY_REDIRECT_URI:
            process.env.API_BASE_URL + "/api/digikey/auth/callback",
          LOG_LEVEL: "info",
          MOUSER_BASE_URL: "https://api.mouser.com/api/v2",
          OPEN_EXCHANGE_RATES_APP_ID: "75080b7d038c410cb47f2ddae788ce38",
          SW_DIGIKEY_CLIENT_ID: "bL4OmERHDGcHX06WwDxHCDtMNdeNEJZ5",
          TI_BASE_URL: "https://transact.ti.com",
          TI_CLIENT_ID: "G3492A6j0ghkBNtHGWIsHPUpIl7JLxS1",
          TI_CLIENT_SECRET: "lAWJDj367bkhEwcQ",
          Z2DATA_API_KEY: "45CZi0Bo75xxii2",
          Z2DATA_BASE_API_URL: "https://gateway.z2data.com",
        },
        containerPort: 8000,
        cpu: "256",
        desiredCount: 1,
        healthCheckPath: "/graphql",
        memory: "512",
        pathPatterns: ["/graphql*", "/api*"],
        priority: 100,
      },
      "market-intelligence": {
        accessToDatabase: false,
        containerEnvironment: {
          BASE_PATH: process.env.MARKET_INTELLIGENCE_BASE_URL_PATH,
          LOG_LEVEL: "info",
          MARKET_INTELLIGENCE_DATABASE_URL:
            "**********************************************************************************************************************************************/gwaihir?schema=production",
          NEXT_PUBLIC_MI_API_BASE_URL: `${process.env.API_BASE_URL}${process.env.MARKET_INTELLIGENCE_BASE_URL_PATH}/api`,
          OPENAI_API_KEY:
            "********************************************************************************************************************************************************************",
          POSTGRES_DATABASE: "gwaihir",
          POSTGRES_HOST:
            "z2data-gwaihir-production-postgres.cep4iscmwcl9.us-east-1.rds.amazonaws.com",
          POSTGRES_PASSWORD: "TemporaryPasswordToBeChangedImmediately!",
          POSTGRES_PORT: "5432",
          POSTGRES_SCHEMA: "production",
          POSTGRES_SSL: "true",
          POSTGRES_USER: "dbadmin",
          TABLE_PREFIX: "",
        },
        containerPort: 3000,
        cpu: "256",
        desiredCount: 1,
        healthCheckPath: "/market-intelligence/api/grade",
        memory: "512",
        pathPatterns: ["/market-intelligence*"],
        priority: 1,
      },
      panel: {
        accessToDatabase: false,
        containerEnvironment: {
          LOG_LEVEL: "info",
          NEXT_PUBLIC_API_BASE: process.env.API_BASE_URL,
          NEXT_PUBLIC_SSO_AUTHORITY: "https://testsso.z2data.com",
          NEXT_PUBLIC_SSO_CLIENT_ID: "97f572a6-f6f5-43c5-ad24-7eac57b7088d",
          NEXT_PUBLIC_SSO_REDIRECT_URI:
            "https://rfq.int.ztwo.app/auth-callback",
          NEXT_PUBLIC_SSO_POST_LOGOUT_REDIRECT_URI: "https://rfq.int.ztwo.app",
        },
        containerPort: 3000,
        cpu: "256",
        desiredCount: 1,
        healthCheckPath: "/",
        memory: "512",
        pathPatterns: ["/*"],
        priority: 1,
      },
    },
    githubOrg: "Z2Data-LLC",
    githubRepo: "swift",
    gwaihirVpcId: "vpc-09fb302103512f039",
    gwaihirPeerConnectionCidrBlock: "10.1.0.0/16",
    imageTag: process.env.IMAGE_TAG || "latest",
    shouldUseExistingGithubOpenIdConnect: false,
    remoteBackend: {
      bucketName: `z2data-swift-terraform-states-production`,
      lockTableName: "terraform-lock-table-production",
      stateKeyPrefix: "swift/production",
    },
    networkConfig: {
      cidrBlock: "10.0.0.0/16",
      publicSubnetCidrBlocks: ["10.0.1.0/24", "10.0.2.0/24"],
      privateSubnetCidrBlocks: ["10.0.3.0/24", "10.0.4.0/24"],
    },
    tags: {
      Environment: "production",
      IACTool: "TerraformCDK",
      Owner: "Z2Data-BI",
      Project: "swift",
    },
  },
  development: {
    awsAccountId: process.env.AWS_ACCOUNT_ID,
    awsRegion: "us-east-1",
    defaultService: "panel",
    customDomainNames: ["rfq-dev.int.ztwo.app"],
    certificateArn:
      "arn:aws:acm:us-east-1:************:certificate/c4ac6fef-1166-4ea5-ac2e-7f53dafdd7f1",
    ecsConfig: {
      "api-service": {
        accessToDatabase: true,
        containerEnvironment: {
          DIGIKEY_BASE_URL: "https://api.digikey.com",
          DIGIKEY_CLIENT_ID: "XAmPwbHwaWeA59xp6WbyKt5yid28ALFf",
          DIGIKEY_CLIENT_SECRET: "O3aUhnYZVUEtGVDA",
          DIGIKEY_DEFAULT_RETURN_URL: process.env.API_BASE_URL,
          DIGIKEY_REDIRECT_URI:
            process.env.API_BASE_URL + "/api/digikey/auth/callback",
          LOG_LEVEL: "info",
          MOUSER_BASE_URL: "https://api.mouser.com/api/v2",
          OPEN_EXCHANGE_RATES_APP_ID: "75080b7d038c410cb47f2ddae788ce38",
          SW_DIGIKEY_CLIENT_ID: "bL4OmERHDGcHX06WwDxHCDtMNdeNEJZ5",
          TI_BASE_URL: "https://transact.ti.com",
          TI_CLIENT_ID: "G3492A6j0ghkBNtHGWIsHPUpIl7JLxS1",
          TI_CLIENT_SECRET: "lAWJDj367bkhEwcQ",
          Z2DATA_API_KEY: "45CZi0Bo75xxii2",
          Z2DATA_BASE_API_URL: "https://gateway.z2data.com",
        },
        containerPort: 8000,
        cpu: "256",
        desiredCount: 1,
        healthCheckPath: "/graphql",
        memory: "512",
        pathPatterns: ["/graphql*", "/api*"],
        priority: 100,
      },
      "market-intelligence": {
        accessToDatabase: false,
        containerEnvironment: {
          BASE_PATH: process.env.MARKET_INTELLIGENCE_BASE_URL_PATH,
          LOG_LEVEL: "info",
          MARKET_INTELLIGENCE_DATABASE_URL:
            "**********************************************************************************************************************************************/gwaihir?schema=production",
          NEXT_PUBLIC_MI_API_BASE_URL: `${process.env.API_BASE_URL}${process.env.MARKET_INTELLIGENCE_BASE_URL_PATH}/api`,
          OPENAI_API_KEY:
            "********************************************************************************************************************************************************************",
          POSTGRES_DATABASE: "gwaihir",
          POSTGRES_HOST:
            "z2data-gwaihir-production-postgres.cep4iscmwcl9.us-east-1.rds.amazonaws.com",
          POSTGRES_PASSWORD: "TemporaryPasswordToBeChangedImmediately!",
          POSTGRES_PORT: "5432",
          POSTGRES_SCHEMA: "production",
          POSTGRES_SSL: "true",
          POSTGRES_USER: "dbadmin",
          TABLE_PREFIX: "",
        },
        containerPort: 3000,
        cpu: "256",
        desiredCount: 1,
        healthCheckPath: "/market-intelligence/api/grade",
        memory: "512",
        pathPatterns: ["/market-intelligence*"],
        priority: 1,
      },
      panel: {
        accessToDatabase: false,
        containerEnvironment: {
          LOG_LEVEL: "info",
          NEXT_PUBLIC_API_BASE: process.env.API_BASE_URL,
          NEXT_PUBLIC_SSO_AUTHORITY: "https://testsso.z2data.com",
          NEXT_PUBLIC_SSO_CLIENT_ID: "97f572a6-f6f5-43c5-ad24-7eac57b7088d",
          NEXT_PUBLIC_SSO_REDIRECT_URI:
            "https://rfq.int.ztwo.app/auth-callback",
          NEXT_PUBLIC_SSO_POST_LOGOUT_REDIRECT_URI: "https://rfq.int.ztwo.app",
        },
        containerPort: 3000,
        cpu: "256",
        desiredCount: 1,
        healthCheckPath: "/",
        memory: "512",
        pathPatterns: ["/*"],
        priority: 1,
      },
    },
    githubOrg: "Z2Data-LLC",
    githubRepo: "swift",
    gwaihirVpcId: "vpc-09fb302103512f039",
    gwaihirPeerConnectionCidrBlock: "10.1.0.0/16",
    imageTag: process.env.IMAGE_TAG || "latest",
    shouldUseExistingGithubOpenIdConnect: true,
    networkConfig: {
      cidrBlock: "20.0.0.0/16",
      publicSubnetCidrBlocks: ["20.0.1.0/24", "20.0.2.0/24"],
      privateSubnetCidrBlocks: ["20.0.3.0/24", "20.0.4.0/24"],
    },
    remoteBackend: {
      bucketName: `z2data-swift-terraform-states-development`,
      lockTableName: "terraform-lock-table-development",
      stateKeyPrefix: "swift/development",
    },
    tags: {
      Environment: "development",
      IACTool: "TerraformCDK",
      Owner: "Z2Data-BI",
      Project: "swift",
    },
  },
} satisfies Record<string, InfrastructureConfig<string>>;
