{"compilerOptions": {"outDir": "../dist/out-tsc", "alwaysStrict": true, "declaration": true, "experimentalDecorators": true, "inlineSourceMap": true, "inlineSources": true, "lib": ["es2018"], "module": "CommonJS", "noEmitOnError": true, "noFallthroughCasesInSwitch": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "resolveJsonModule": true, "strict": true, "strictNullChecks": true, "strictPropertyInitialization": true, "stripInternal": true, "target": "ES2018", "incremental": true, "skipLibCheck": true}, "include": ["**/*.ts"], "exclude": ["node_modules", "cdktf.out"], "files": ["src/types.d.ts"]}