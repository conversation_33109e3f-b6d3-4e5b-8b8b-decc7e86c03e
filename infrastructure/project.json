{"name": "infrastructure", "root": "infrastructure", "sourceRoot": "infrastructure/src", "projectType": "application", "targets": {"synth": {"executor": "nx:run-commands", "options": {"command": "cdktf synth", "cwd": "infrastructure"}, "defaultConfiguration": "development", "configurations": {"development": {"env": {"ENVIRONMENT": "development"}}, "production": {"env": {"ENVIRONMENT": "production"}}}}, "deploy-bootstrap": {"executor": "nx:run-commands", "options": {"command": "cdktf deploy bootstrap-stack --auto-approve", "cwd": "infrastructure"}, "defaultConfiguration": "development", "configurations": {"development": {"env": {"ENVIRONMENT": "development"}}, "production": {"env": {"ENVIRONMENT": "production"}}}}, "deploy-minimum-stack": {"executor": "nx:run-commands", "options": {"command": "cdktf deploy bootstrap-stack ecr-stack auth-stack network-stack --auto-approve", "cwd": "infrastructure"}, "defaultConfiguration": "development", "configurations": {"development": {"env": {"ENVIRONMENT": "development"}}, "production": {"env": {"ENVIRONMENT": "production"}}}}, "deploy-shared-cluster": {"executor": "nx:run-commands", "options": {"command": "cdktf deploy bootstrap-stack ecr-stack kms-stack auth-stack network-stack ecs-stack database-stack cloudfront-stack --auto-approve", "cwd": "infrastructure"}, "defaultConfiguration": "development", "configurations": {"development": {"env": {"ENVIRONMENT": "development"}}, "production": {"env": {"ENVIRONMENT": "production"}}}}, "destroy": {"executor": "nx:run-commands", "options": {"command": "cdktf destroy '*' --auto-approve", "cwd": "infrastructure"}}, "plan": {"executor": "nx:run-commands", "options": {"command": "cdktf plan", "cwd": "infrastructure"}}, "diff": {"executor": "nx:run-commands", "options": {"command": "cdktf diff", "cwd": "infrastructure"}}, "output": {"executor": "nx:run-commands", "options": {"command": "terraform -chdir=infrastructure/cdktf.out/stacks/{args.stack} output -json > dist/{args.stack}.json"}}, "get": {"executor": "nx:run-commands", "options": {"command": "cdktf get", "cwd": "infrastructure"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["infrastructure/**/*.ts"], "maxWarnings": 0}}, "test": {"options": {"passWithNoTests": false}}, "ts-check": {"executor": "nx:run-commands", "options": {"command": "tsc --build --force --verbose infrastructure/tsconfig.json"}}}}