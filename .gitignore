# See https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files for more about ignoring files.

# compiled output
dist
tmp
out-tsc
sample

# dependencies
node_modules

# Generated files
**/generated

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# IDE - Cursor
.cursor

# misc
/.sass-cache
/connect.lock
/coverage
/libpeerconnection.log
npm-debug.log
yarn-error.log
testem.log
/typings

# System Files
.DS_Store
Thumbs.db

.nx/cache
.nx/workspace-data

test-output

# Ignore all .env files anywhere in the project
**/.env
**/.env.*
!**/.env.sample

# TypeScript build info
*.tsbuildinfo

/.nvmrc
/CLAUDE.local.md
/CLAUDE.md
/.claude
/plans/

.cursor\rules\nx-rules.mdc
.github\instructions\nx.instructions.md
/.vscode
/apps/api-service/.plan
/apps/api-service/src/z2data/docs
/apps/panel/src/lib/*.md
